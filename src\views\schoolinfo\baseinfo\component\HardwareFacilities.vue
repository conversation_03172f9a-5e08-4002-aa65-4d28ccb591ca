<template>
  <div class="hardware-facilities">
    <!-- 足球场规格 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <div class="title-text">足球场规格</div>
      </div>
      <div class="field-specs">
        <div class="spec-item">
          <div class="spec-label">标准11制</div>
        </div>
      </div>
    </div>

    <!-- 足球场设备 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <div class="title-text">足球场设备</div>
      </div>
      <div class="equipment-grid">
        <div class="equipment-item">
          <img src="@/assets/images/sports-equipment (1).png" alt="足球" class="equipment-icon">
          <div class="equipment-count">50个</div>
        </div>
        <div class="equipment-item">
          <img src="@/assets/images/sports-equipment (2).png" alt="训练锥" class="equipment-icon">
          <div class="equipment-count">100个</div>
        </div>
        <div class="equipment-item">
          <img src="@/assets/images/sports-equipment (3).png" alt="标志杆" class="equipment-icon">
          <div class="equipment-count">60个</div>
        </div>
        <div class="equipment-item">
          <img src="@/assets/images/sports-equipment (4).png" alt="角旗" class="equipment-icon">
          <div class="equipment-count">30个</div>
        </div>
        <div class="equipment-item">
          <img src="@/assets/images/sports-equipment (5).png" alt="训练背心" class="equipment-icon">
          <div class="equipment-count">90件</div>
        </div>
      </div>
    </div>

    <!-- 训练设备 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <div class="title-text">训练设备</div>
      </div>
      <div class="equipment-grid">
        <div class="equipment-item">
          <img src="@/assets/images/training-equipment (1).png" alt="训练设备1" class="equipment-icon">
          <div class="equipment-count">5套</div>
        </div>
        <div class="equipment-item">
          <img src="@/assets/images/training-equipment (2).png" alt="训练设备2" class="equipment-icon">
          <div class="equipment-count">100个</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
defineOptions({
    name: 'hardware-facilities'
})
</script>

<style src="./styles.css"></style>
<style scoped lang="scss">
.hardware-facilities {
  padding: 20px;

  .section {
    margin-bottom: 40px;

    .field-specs {
      .spec-item {
        .spec-label {
          font-size: 18px;
          font-weight: 600;
          color: #ffffff;
          background: rgba(45, 216, 183, 0.2);
          padding: 12px 24px;
          border-radius: 25px;
          border: 2px solid #2DD8B7;
          display: inline-block;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 12px rgba(45, 216, 183, 0.3);
        }
      }
    }

    .equipment-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;

      .equipment-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 120px;

        .equipment-icon {
          width: 80px;
          height: 80px;
          object-fit: contain;
          margin-bottom: 12px;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .equipment-count {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          text-align: center;
          background: rgba(255, 255, 255, 0.1);
          padding: 6px 12px;
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          backdrop-filter: blur(10px);
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 15px;

    .section {
      margin-bottom: 30px;

      .equipment-grid {
        gap: 20px;
        justify-content: center;

        .equipment-item {
          min-width: 100px;

          .equipment-icon {
            width: 60px;
            height: 60px;
          }

          .equipment-count {
            font-size: 14px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
}
</style>
