<template>
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    import { ref, markRaw } from 'vue';
    import TabsSwitch from '@/components/TabsSwitch-el.vue';
    import matchdatafilter from '@/views/schoolinfo/matchdata/component/matchdatafilter.vue'; // 导入组件
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '全部', component: matchdatafilter },
        { name: '胜', component: matchdatafilter },
        { name: '平', component: matchdatafilter },
        { name: '负', component: matchdatafilter }
    ]))
</script>


<style scoped>

</style>
