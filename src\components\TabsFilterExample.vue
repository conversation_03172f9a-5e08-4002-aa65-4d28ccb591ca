<template>
  <div class="tabs-filter-example">
    <h2>TabsFilter 使用示例</h2>
    
    <!-- 使用改造后的TabsFilter组件 -->
    <TabsFilter
      :tabs="tabsConfig"
      :fixed-component="PlayerList"
      :default-active="0"
      @tab-change="handleTabChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TabsFilter from './TabsFilter.vue'
import PlayerList from './PlayerList.vue' // 假设的球员列表组件

// tabs配置
const tabsConfig = ref([
  {
    title: '全部',
    name: 'all',
    data: {
      position: 'all',
      players: [
        { id: 1, name: '张三', position: '前锋', number: 10 },
        { id: 2, name: '李四', position: '中场', number: 8 },
        { id: 3, name: '王五', position: '后卫', number: 5 },
        { id: 4, name: '赵六', position: '守门员', number: 1 }
      ]
    }
  },
  {
    title: '前锋',
    name: 'forward',
    data: {
      position: 'forward',
      players: [
        { id: 1, name: '张三', position: '前锋', number: 10 }
      ]
    }
  },
  {
    title: '中场',
    name: 'midfielder',
    data: {
      position: 'midfielder',
      players: [
        { id: 2, name: '李四', position: '中场', number: 8 }
      ]
    }
  },
  {
    title: '后卫',
    name: 'defender',
    data: {
      position: 'defender',
      players: [
        { id: 3, name: '王五', position: '后卫', number: 5 }
      ]
    }
  },
  {
    title: '守门员',
    name: 'goalkeeper',
    data: {
      position: 'goalkeeper',
      players: [
        { id: 4, name: '赵六', position: '守门员', number: 1 }
      ]
    }
  }
])

// 处理tab切换事件
const handleTabChange = (event) => {
  console.log('Tab切换:', event)
  console.log('当前位置:', event.data.position)
  console.log('球员列表:', event.data.players)
}
</script>

<style scoped lang="scss">
.tabs-filter-example {
  padding: 20px;
  
  h2 {
    color: #fff;
    margin-bottom: 30px;
    font-size: 24px;
  }
}
</style>
