<template>
  <div class="trainingoutline">
    
    <!-- 详情内容 -->
    <div class="details-content">
      <!-- 第一行 -->
      <div class="details-row">
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">时间</span>
        </div>
          <div class="detail-value">6月1日-6月30日</div>
        </div>
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">地点</span>
        </div>
          <div class="detail-value">
            <span class="adress-value">深圳某某足球场</span>
          </div>
        </div>
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">人员</span>
        </div>
          <div class="detail-value">全体人员</div>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="details-row">
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">训练内容</span>
        </div>
          <div class="detail-value">绕场慢跑400米，穿插侧滑步、高抬腿、后踢腿；关节活动：踝关节绕环、膝关节屈伸、髋部扭转、摆臂扩胸；动态拉伸：弓步转体、踢臀跑、行进间抱膝提踵</div>
        </div>
        
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style src="@/styles/styles.scss"></style>
<style scoped lang="scss">
.trainingoutline {
  padding: 30px;
  padding-top: 0;
  color: #fff;

  // 详情内容样式
  .details-content {
    .details-row {
      display: flex;
      margin-bottom: 60px;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-item {
        flex: 1;
        margin-right: 60px;

        &:last-child {
          margin-right: 0;
        }

        .detail-label {
          font-size: 14px;
          color: #00D4FF;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .detail-value {
          font-size: 24px;
          color: #ffffff;
          line-height: 1.4;

          .adress-value {
            display: inline-block;
            margin-right: 20px;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .details-content {
      .details-row {
        flex-wrap: wrap;

        .detail-item {
          flex: 0 0 50%;
          margin-bottom: 20px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 20px;

    .details-content {
      .details-row {
        flex-direction: column;

        .detail-item {
          margin-right: 0;
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>

