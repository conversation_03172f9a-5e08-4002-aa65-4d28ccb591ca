<template>
	<div>
    <el-table :data="searchValue" style="width: 100%" class="custom-table" stripe=true>
      <el-table-column prop="jerseyNumber" label="球衣号" width="180" />
      <el-table-column prop="name" label="姓名" width="180" />
      <el-table-column prop="age" label="年龄" />
      <el-table-column prop="position" label="位置" />
      <el-table-column prop="appearances" label="出场" />
      <el-table-column prop="goals" label="进球" />
      <el-table-column prop="assists" label="助攻" />
      <el-table-column prop="aiScore" label="AI评分" />
      <el-table-column prop="grade" label="球队" />
      <el-table-column prop="aiArchives" label="档案" />
  </el-table>
  </div>
</template>
<script setup>
import { ref } from 'vue';

let searchValue = ref();
searchValue.value = [
  {
    id: 1,
    jerseyNumber: '10',      // 球衣号
    name: '张三',            // 姓名
    age: 23,                 // 年龄
    position: '前锋',        // 位置
    appearances: 15,         // 出场
    goals: 8,               // 进球
    assists: 5,             // 助攻
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true              // 评级
  },
  {
    id: 2,
    jerseyNumber: '7',
    name: '李四',
    age: 25,
    position: '中场',
    appearances: 18,
    goals: 3,
    assists: 12,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 3,
    jerseyNumber: '5',
    name: '王五',
    age: 27,
    position: '后卫',
    appearances: 20,
    goals: 1,
    assists: 2,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 4,
    jerseyNumber: '1',
    name: '赵六',
    age: 29,
    position: '守门员',
    appearances: 22,
    goals: 0,
    assists: 0,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 5,
    jerseyNumber: '11',
    name: '钱七',
    age: 21,
    position: '前锋',
    appearances: 12,
    goals: 6,
    assists: 3,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 6,
    jerseyNumber: '8',
    name: '孙八',
    age: 24,
    position: '中场',
    appearances: 16,
    goals: 4,
    assists: 8,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 7,
    jerseyNumber: '3',
    name: '周九',
    age: 26,
    position: '后卫',
    appearances: 19,
    goals: 0,
    assists: 1,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 8,
    jerseyNumber: '9',
    name: '吴十',
    age: 22,
    position: '前锋',
    appearances: 14,
    goals: 7,
    assists: 4,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true

  }
];

</script>
<style scoped>
.custom-table {
  /* 表格整体背景 - 完全透明 */
  background: transparent;
  border-radius: 0;
}

/* 表头样式 */
.custom-table :deep(.el-table__header) {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* .custom-table :deep(.el-table__header th) {
  background: rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  font-weight: 500;
  font-size: 14px;
  padding: 12px 8px;
} */
 .custom-table :deep(.el-table__header th){
  background: transparent !important;
 }

/* 表格主体行样式 */
.custom-table :deep(.el-table__body tr) {
  background: rgba(255, 255, 255, 0.02) !important;
  color: #ffffff !important;
}




.custom-table :deep(.el-table__body td) {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
  padding: 12px 8px;
  font-size: 14px;
}

/* 移除表格边框 */
.custom-table :deep(.el-table) {
  border: none !important;
}

.custom-table :deep(.el-table::before) {
  display: none;
}

.custom-table :deep(.el-table--border) {
  border: none !important;
}

.custom-table :deep(.el-table--border::after) {
  display: none;
}

/* 空状态样式 */
/* .custom-table :deep(.el-table__empty-block) {
  background: transparent !important;
  color: #ffffff !important;
} */

.custom-table :deep(.el-table__empty-text) {
  color: rgba(255, 255, 255, 0.6) !important;
}
</style>
