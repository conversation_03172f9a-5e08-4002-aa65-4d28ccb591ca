<template>
	<div>
    <el-table :data="searchValue" style="width: 100%" class="custom-table"  stripe
      :cell-style="{color:'#fff',background: 'transparent'}"
      :header-row-style="{color:'#fff',background: 'rgba(8,139,255,0.1)'}"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="jerseyNumber" label="球衣号" width="180"  />
      <el-table-column prop="name" label="姓名" width="180">
          <template #default="scope">
            <div class="name-cell">
              <img :src="scope.row.avatar" alt="" class="avatar">
              <span>{{ scope.row.name }}</span>
              
              <div v-if="scope.row.aiArchives" class="isTop">南山精英</div>
            </div>
          </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄" />
      <el-table-column prop="position" label="位置" />
      <el-table-column prop="appearances" label="出场" />
      <el-table-column prop="goals" label="进球" />
      <el-table-column prop="assists" label="助攻" />
      <el-table-column prop="aiScore" label="AI评分" />
      <el-table-column prop="grade" label="球队" />
      <el-table-column prop="aiArchives" label="档案">
        <template #default="scope">
            <div v-if="scope.row.aiArchives" class="aiArchives">AI档案</div>
            <span v-else></span>
          </template>
      </el-table-column>
  </el-table>
  </div>
</template>
<script setup>
import { ref} from 'vue';
import studentImg from '@/assets/images/coach-01.png'
import { watchRoute } from '@/utils/watchRoute';
let searchValue = ref();
searchValue.value = [
  {
    id: 1,
    jerseyNumber: '10',      // 球衣号
    name: '张三',         // 姓名
    avatar:studentImg,
    age: 23,                 // 年龄
    position: '前锋',        // 位置
    appearances: 15,         // 出场
    goals: 8,               // 进球
    assists: 5,             // 助攻
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true              // AI档案
  },
  {
    id: 2,
    jerseyNumber: '7',
    name: '李四',
    avatar:studentImg,
    age: 25,
    position: '中场',
    appearances: 18,
    goals: 3,
    assists: 12,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 3,
    jerseyNumber: '5',
    name: '王五',
    avatar:studentImg,
    age: 27,
    position: '后卫',
    appearances: 20,
    goals: 1,
    assists: 2,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 4,
    jerseyNumber: '1',
    name: '赵六',
    avatar:studentImg,
    age: 29,
    position: '守门员',
    appearances: 22,
    goals: 0,
    assists: 0,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 5,
    jerseyNumber: '11',
    name: '钱七',
    avatar:studentImg,
    age: 21,
    position: '前锋',
    appearances: 12,
    goals: 6,
    assists: 3,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 6,
    jerseyNumber: '8',
    name: '孙八',
    avatar:studentImg,
    age: 24,
    position: '中场',
    appearances: 16,
    goals: 4,
    assists: 8,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 7,
    jerseyNumber: '3',
    name: '周九',
    avatar:studentImg,
    age: 26,
    position: '后卫',
    appearances: 19,
    goals: 0,
    assists: 1,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 8,
    jerseyNumber: '9',
    name: '吴十',
    avatar:studentImg,
    age: 22,
    position: '前锋',
    appearances: 14,
    goals: 7,
    assists: 4,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true

  }
];
//table 结合内置方法改变th、td样式，隔行变色设置
function tableRowClassName({ row, rowIndex }) {
	if (rowIndex % 2 === 1) {
		return "warning-row";
	} else {
		return "success-row";
	}
}
const params = watchRoute(); 
console.log(params);
</script>

<style src="@/styles/table.scss" lang="scss" scoped></style>
<style scoped lang="scss">
.name-cell{
  display: flex;
  align-items: center;
  .avatar{
    width: 21px;
    height: 21px;
    margin-right: 10px;
  }
  .isTop{
    height: 16px;
    line-height: 16px;
    border: 1px solid #FF8B00;
    color: #FF8B00;
    padding: 0px 4px;
    border-radius: 9px;
    font-size: 9px;
    margin-left: 10px;
  }
}
</style>
