<template>
	<div>搜索结果</div>
</template>
<script setup>
import { ref } from 'vue';

let searchValue = ref();
searchValue.value = [
  {
    id: 1,
    jerseyNumber: '10',      // 球衣号
    name: '张三',            // 姓名
    age: 23,                 // 年龄
    position: '前锋',        // 位置
    appearances: 15,         // 出场
    goals: 8,               // 进球
    assists: 5,             // 助攻
    rating: 8.5,            // 入评分
    grade: 'A'              // 评级
  },
  {
    id: 2,
    jerseyNumber: '7',
    name: '李四',
    age: 25,
    position: '中场',
    appearances: 18,
    goals: 3,
    assists: 12,
    rating: 7.8,
    grade: 'B+'
  },
  {
    id: 3,
    jerseyNumber: '5',
    name: '王五',
    age: 27,
    position: '后卫',
    appearances: 20,
    goals: 1,
    assists: 2,
    rating: 7.2,
    grade: 'B'
  },
  {
    id: 4,
    jerseyNumber: '1',
    name: '赵六',
    age: 29,
    position: '守门员',
    appearances: 22,
    goals: 0,
    assists: 0,
    rating: 7.5,
    grade: 'B+'
  },
  {
    id: 5,
    jerseyNumber: '11',
    name: '钱七',
    age: 21,
    position: '前锋',
    appearances: 12,
    goals: 6,
    assists: 3,
    rating: 7.9,
    grade: 'B+'
  },
  {
    id: 6,
    jerseyNumber: '8',
    name: '孙八',
    age: 24,
    position: '中场',
    appearances: 16,
    goals: 4,
    assists: 8,
    rating: 8.1,
    grade: 'A-'
  },
  {
    id: 7,
    jerseyNumber: '3',
    name: '周九',
    age: 26,
    position: '后卫',
    appearances: 19,
    goals: 0,
    assists: 1,
    rating: 6.9,
    grade: 'B-'
  },
  {
    id: 8,
    jerseyNumber: '9',
    name: '吴十',
    age: 22,
    position: '前锋',
    appearances: 14,
    goals: 7,
    assists: 4,
    rating: 8.3,
    grade: 'A-'
  }
];

</script>
<style scoped>

</style>
