<template>
	<div>
    <el-table :data="searchValue" style="width: 100%" class="custom-table"  stripe=true >
      <el-table-column prop="jerseyNumber" label="球衣号" width="180" />
      <el-table-column prop="name" label="姓名" width="180" />
      <el-table-column prop="age" label="年龄" />
      <el-table-column prop="position" label="位置" />
      <el-table-column prop="appearances" label="出场" />
      <el-table-column prop="goals" label="进球" />
      <el-table-column prop="assists" label="助攻" />
      <el-table-column prop="aiScore" label="AI评分" />
      <el-table-column prop="grade" label="球队" />
      <el-table-column prop="aiArchives" label="档案" />
  </el-table>
  </div>
</template>
<script setup>
import { ref } from 'vue';

let searchValue = ref();
searchValue.value = [
  {
    id: 1,
    jerseyNumber: '10',      // 球衣号
    name: '张三',            // 姓名
    age: 23,                 // 年龄
    position: '前锋',        // 位置
    appearances: 15,         // 出场
    goals: 8,               // 进球
    assists: 5,             // 助攻
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true              // 评级
  },
  {
    id: 2,
    jerseyNumber: '7',
    name: '李四',
    age: 25,
    position: '中场',
    appearances: 18,
    goals: 3,
    assists: 12,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 3,
    jerseyNumber: '5',
    name: '王五',
    age: 27,
    position: '后卫',
    appearances: 20,
    goals: 1,
    assists: 2,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 4,
    jerseyNumber: '1',
    name: '赵六',
    age: 29,
    position: '守门员',
    appearances: 22,
    goals: 0,
    assists: 0,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 5,
    jerseyNumber: '11',
    name: '钱七',
    age: 21,
    position: '前锋',
    appearances: 12,
    goals: 6,
    assists: 3,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:false
  },
  {
    id: 6,
    jerseyNumber: '8',
    name: '孙八',
    age: 24,
    position: '中场',
    appearances: 16,
    goals: 4,
    assists: 8,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 7,
    jerseyNumber: '3',
    name: '周九',
    age: 26,
    position: '后卫',
    appearances: 19,
    goals: 0,
    assists: 1,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true
  },
  {
    id: 8,
    jerseyNumber: '9',
    name: '吴十',
    age: 22,
    position: '前锋',
    appearances: 14,
    goals: 7,
    assists: 4,
    aiScore: 88,            // AI评分
    grade: 'U12',
    aiArchives:true

  }
];

</script>
<style scoped lang="scss">
:deep(.el-table),
:deep(.el-table th),
:deep(.el-table tr),
:deep(.el-table td) {
  background-color: transparent !important;
  color: #fff !important;
}

:deep(.el-table__body tr:hover > td) {
  background-color: rgba(200, 200, 200, 0.2) !important;
}
.custom-table {
  /* 穿透作用域，覆盖 Element Plus 样式 */
  :deep(.el-table::after) {
    display: none !important;
  }
  :deep(.el-table__body-wrapper) {
    border-bottom: none !important;
  }
}
:deep(.el-table td),
:deep(.el-table th) {
  border-bottom: 0 !important;
}

/* 确保去掉表格底部的所有边框线 */
:deep(.el-table) {
  border: none !important;
}

:deep(.el-table::before) {
  display: none !important;
}

:deep(.el-table--border) {
  border: none !important;
}

:deep(.el-table--border::after) {
  display: none !important;
}

:deep(.el-table__inner-wrapper::after) {
  display: none !important;
}
</style>
