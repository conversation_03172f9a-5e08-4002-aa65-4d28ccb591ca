<template>
  <div class="conatiner">
    <TitleInfo title="南山特色足球学校" beautiful="Nanshan Characteristic Football School" />
    <div  class="school-info">
        <div class="back"><img src="@/assets/images/back.png" alt="" srcset=""></div>
        <div class="school-show">
          <img src="@/assets/images/school_icon.png" alt="" srcset="">
          <div class="school-tag">
            <p class="school-name">南外文化学校</p>
            <p class="school-special"><span>全国青少年校园足球特色学校</span><span>区级青训基地</span></p>
          </div>
        </div>
        <div class="school-menu">
          <ul class="menu-list">
            <li
              v-for="(item, index) in menuItems"
              :key="index"
              :class="{ active: activeIndex === index }"
              @click="switchMenu(index)"
              :to="item.path"
            >
              {{ item.name }}
            </li>
        </ul>
        </div>
    </div>
    <div class="school-content">
      <RouterView />
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import TitleInfo from '@/components/TitleInfo.vue';
import { useRouter } from 'vue-router';
const router = useRouter();
// 菜单项数据
// const menuItems = ref(['基本信息', '球员信息', '比赛数据', '人才输送']);

const menuItems=ref([{name:'基本信息',path:'baseinfo'},{name:'球员信息',path:'scoreinfo'},{name:'比赛数据',path:'matchdata'},{name:'人才输送',path:'talenttransfer'}])
// 当前激活的菜单索引
const activeIndex = ref(0);

// 切换菜单函数
const switchMenu = (index) => {
  activeIndex.value = index;
  // 这里可以添加切换内容的逻辑
  console.log(`切换到菜单: ${menuItems.value[index].name}`);
  router.push(menuItems.value[index].path);
};
</script>
<style scoped lang="scss">
    .conatiner{
        width: 100%;
        height: 100%;
        .school-info{
          width: 100%;
          height: 95px;
          display: flex;
          .back{
            width: 35px;
            height: 30px;
            cursor: pointer;
            margin-top: 20px;
            margin-left: 12px;
            margin-right: 35px;
            img{
                width: 100%;
                height: 100%;
            }
          }
          .school-show{
            display: flex;
            img{
              width: 80px;
              height: 80px;
              
            }
            .school-tag{
              margin-left: 20px;

              .school-special{
                span{
                  font-size: 9px;
                  padding: 4px 10px;
                  color: #0EDEF1;
                  border: 1px solid #0EDEF1;
                  border-radius: 13px;
                  margin-right: 10px;
                }
              }
            }
            .school-name{
              font-size: 35px;
              font-weight: 500;
              color: #fff;
              
            }
          }
          .school-menu{
            margin-left: auto;
            margin-top: 23px;
          }
          .menu-list{
            display: flex;
            align-items: flex-end;
            li{
              cursor: pointer;
              font-size: 18px;
              color: #fff;
              width: 203px;
              height: 62px;
              line-height: 62px;
              text-align: center;
              background-image: url(@/assets/images/schoolinfo-noselect.png);
              background-size: 100% 100%;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              transform-origin: bottom center;
              position: relative;

              &:hover {
                transform: translateY(-2px);
                filter: brightness(1.1);
              }
            }
            .active{
              width: 231px;
              height: 72px;
              line-height: 72px;
              background-image: url(@/assets/images/schoolinfo-select.png);
              font-size: 20px;
              color: #2DD8B7;
              font-weight: 800;
              z-index: 2;

              &:hover {
                transform: translateY(-5px);
              }
            }
          }
        }
        .school-content{
          width: 1495px;
          height: 575px;
          margin: 0 auto;
          background: rgba(1,31,81,0.7);
          border-radius: 10px;
          margin-top: 10px;
        }
    }
</style>