<template>
  <div class="conatiner">
    <TitleInfo title="南山特色足球学校" beautiful="Nanshan Characteristic Football School" />
    <div  class="school-info">
        <div class="back"><img src="@/assets/images/back.png" alt="" srcset=""></div>
        <div class="school-show">
          <img src="@/assets/images/school_icon.png" alt="" srcset="">
          <div class="school-tag">
            <p class="school-name">南外文化学校</p>
            <p class="school-special"><span>全国青少年校园足球特色学校</span><span>区级青训基地</span></p>
          </div>
        </div>
        <div class="school-menu">
          <ul class="menu-list">
            <li class="active">基本信息</li>
            <li>球员信息</li>
            <li>比赛数据</li>
            <li>人才输送</li>
        </ul>
        </div>
    </div>
    <div></div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
    import TitleInfo from '@/components/TitleInfo.vue';
</script>
<style scoped lang="scss">
    .conatiner{
        width: 100%;
        height: 100%;
        .school-info{
          width: 100%;
          display: flex;
          .back{
            width: 35px;
            height: 30px;
            cursor: pointer;
            margin-top: 20px;
            margin-left: 12px;
            margin-right: 35px;
            img{
                width: 100%;
                height: 100%;
            }
          }
          .school-show{
            display: flex;
            img{
              width: 80px;
              height: 80px;
              
            }
            .school-tag{
              margin-left: 20px;

              .school-special{
                span{
                  font-size: 9px;
                  padding: 4px 10px;
                  color: #0EDEF1;
                  border: 1px solid #0EDEF1;
                  border-radius: 13px;
                  margin-right: 10px;
                }
              }
            }
            .school-name{
              font-size: 35px;
              font-weight: 500;
              color: #fff;
              
            }
          }
          .school-menu{
            margin-left: auto;
            margin-top: 23px;
          }
          .menu-list{
            display: flex;
            li{
              cursor: pointer;
              font-size: 18px;
              color: #fff;
              width:203px;
              height: 62px;
              line-height: 62px;
              text-align: center;
              background-image: url(@/assets/images/school-noselect.png);
              background-size: 100% 100%;
              .active{
                width: 231px;
                height: 72px;
                background-size: 100% 100%;
                background-image: url(@/assets/images/school-select.png);
              }
            }
          }
        }
    }
</style>