<template>
  <div class="match-filter-example">
    <h2>比赛结果筛选示例</h2>
    
    <!-- 基础筛选 -->
    <div class="filter-section">
      <h3>基础结果筛选</h3>
      <div class="filter-buttons">
        <button 
          v-for="result in resultOptions" 
          :key="result.value"
          @click="handleKeyChange(result.value)"
          :class="{ active: currentFilter === result.value }"
        >
          {{ result.label }}
        </button>
      </div>
    </div>
    
    <!-- 高级筛选 -->
    <div class="filter-section">
      <h3>高级筛选</h3>
      <div class="advanced-filters">
        <div class="filter-item">
          <label>比赛结果:</label>
          <select v-model="advancedFilters.result">
            <option value="全部">全部</option>
            <option value="W">胜</option>
            <option value="L">负</option>
            <option value="D">平</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label>最少进球:</label>
          <input 
            type="number" 
            v-model.number="advancedFilters.minScore" 
            min="0" 
            placeholder="最少进球数"
          >
        </div>
        
        <div class="filter-item">
          <label>对手名称:</label>
          <input 
            type="text" 
            v-model="advancedFilters.opponent" 
            placeholder="搜索对手"
          >
        </div>
        
        <div class="filter-item">
          <label>开始日期:</label>
          <input 
            type="date" 
            v-model="advancedFilters.startDate"
          >
        </div>
        
        <div class="filter-item">
          <label>结束日期:</label>
          <input 
            type="date" 
            v-model="advancedFilters.endDate"
          >
        </div>
        
        <div class="filter-actions">
          <button @click="applyAdvancedFilter" class="apply-btn">应用筛选</button>
          <button @click="resetAllFilters" class="reset-btn">重置</button>
        </div>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-section">
      <h3>比赛统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">总场次:</span>
          <span class="stat-value">{{ statistics.total }}</span>
        </div>
        <div class="stat-item win">
          <span class="stat-label">胜:</span>
          <span class="stat-value">{{ statistics.wins }}</span>
        </div>
        <div class="stat-item loss">
          <span class="stat-label">负:</span>
          <span class="stat-value">{{ statistics.losses }}</span>
        </div>
        <div class="stat-item draw">
          <span class="stat-label">平:</span>
          <span class="stat-value">{{ statistics.draws }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">胜率:</span>
          <span class="stat-value">{{ statistics.winRate }}%</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">场均进球:</span>
          <span class="stat-value">{{ statistics.avgGoals }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">场均失球:</span>
          <span class="stat-value">{{ statistics.avgConceded }}</span>
        </div>
      </div>
    </div>
    
    <!-- 快速筛选 -->
    <div class="quick-filters">
      <h3>快速筛选</h3>
      <div class="quick-filter-buttons">
        <button @click="filterRecentWins">近期胜利</button>
        <button @click="filterHighScoringGames">高比分比赛</button>
        <button @click="filterCloseGames">激烈比赛</button>
        <button @click="filterHomeAdvantage">主场优势</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 筛选选项
const resultOptions = [
  { label: '全部', value: '全部' },
  { label: '胜利', value: '胜' },
  { label: '失败', value: '负' },
  { label: '平局', value: '平' }
]

// 当前筛选状态
const currentFilter = ref('全部')

// 高级筛选条件
const advancedFilters = ref({
  result: '全部',
  minScore: undefined,
  opponent: '',
  startDate: '',
  endDate: ''
})

// 统计信息
const statistics = ref({
  total: 0,
  wins: 0,
  losses: 0,
  draws: 0,
  winRate: '0.0',
  avgGoals: '0.0',
  avgConceded: '0.0'
})

// 基础筛选方法（调用父组件的方法）
const handleKeyChange = (key) => {
  currentFilter.value = key
  // 这里调用父组件的筛选方法
  console.log('筛选:', key)
  // 实际使用时需要调用: parent.handleKeyChange(key)
}

// 应用高级筛选
const applyAdvancedFilter = () => {
  const filters = {
    result: advancedFilters.value.result === '全部' ? undefined : 
           advancedFilters.value.result === '胜' ? 'W' :
           advancedFilters.value.result === '负' ? 'L' : 'D',
    minScore: advancedFilters.value.minScore,
    opponent: advancedFilters.value.opponent,
    startDate: advancedFilters.value.startDate,
    endDate: advancedFilters.value.endDate
  }
  
  console.log('应用高级筛选:', filters)
  // 实际使用时需要调用: parent.filterMatches(filters)
}

// 重置所有筛选
const resetAllFilters = () => {
  currentFilter.value = '全部'
  advancedFilters.value = {
    result: '全部',
    minScore: undefined,
    opponent: '',
    startDate: '',
    endDate: ''
  }
  console.log('重置所有筛选')
  // 实际使用时需要调用: parent.resetFilter()
}

// 快速筛选方法
const filterRecentWins = () => {
  console.log('筛选近期胜利')
  // 筛选最近5场胜利
}

const filterHighScoringGames = () => {
  console.log('筛选高比分比赛')
  // 筛选进球数>=3的比赛
}

const filterCloseGames = () => {
  console.log('筛选激烈比赛')
  // 筛选比分差距<=1的比赛
}

const filterHomeAdvantage = () => {
  console.log('筛选主场优势比赛')
  // 筛选主场比客场表现更好的比赛
}

// 更新统计信息
const updateStatistics = () => {
  // 这里应该调用父组件的统计方法
  // statistics.value = parent.getMatchStatistics()
}

onMounted(() => {
  updateStatistics()
})
</script>

<style scoped lang="scss">
.match-filter-example {
  padding: 20px;
  color: #fff;
}

.filter-section {
  margin-bottom: 30px;
  
  h3 {
    color: #2DD8B7;
    margin-bottom: 15px;
  }
}

.filter-buttons {
  display: flex;
  gap: 10px;
  
  button {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
    
    &.active {
      background: #2DD8B7;
      border-color: #2DD8B7;
    }
  }
}

.advanced-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  
  .filter-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    
    label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
    
    input, select {
      padding: 8px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      border-radius: 4px;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
  
  .filter-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 10px;
    
    .apply-btn {
      background: #2DD8B7;
      color: #fff;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .reset-btn {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      border: 1px solid rgba(255, 255, 255, 0.2);
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  
  .stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    
    .stat-label {
      display: block;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 5px;
    }
    
    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #fff;
    }
    
    &.win .stat-value {
      color: #4CAF50;
    }
    
    &.loss .stat-value {
      color: #F44336;
    }
    
    &.draw .stat-value {
      color: #FF9800;
    }
  }
}

.quick-filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  
  button {
    padding: 8px 16px;
    background: rgba(45, 216, 183, 0.2);
    border: 1px solid #2DD8B7;
    color: #2DD8B7;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background: #2DD8B7;
      color: #fff;
    }
  }
}
</style>
