<template>
  <div class="tabs-el-demo">
    <div class="demo-header">
      <h2>Element Plus TabsSwitch 演示</h2>
      <p>基于Element Plus的标签切换组件</p>
    </div>
    
    <!-- 基础用法 -->
    <div class="demo-section">
      <h3>基础用法</h3>
      <TabsSwitchEl 
        :tabs="basicTabs"
        @change="onTabChange"
        @tab-click="onTabClick"
      />
    </div>
    
    <!-- 指定默认激活标签 -->
    <div class="demo-section">
      <h3>指定默认激活标签（索引方式）</h3>
      <TabsSwitchEl 
        :tabs="basicTabs"
        :default-active="2"
      />
    </div>
    
    <!-- 指定默认激活标签（名称方式） -->
    <div class="demo-section">
      <h3>指定默认激活标签（名称方式）</h3>
      <TabsSwitchEl 
        :tabs="basicTabs"
        default-active="师资力量"
      />
    </div>
    
    <!-- 传递Props给组件 -->
    <div class="demo-section">
      <h3>传递Props给组件</h3>
      <TabsSwitchEl 
        :tabs="propsTabsExample"
        :component-props="componentProps"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TabsSwitchEl from '@/components/TabsSwitch-el.vue'
import BasicInfo from '@/components/demo/BasicInfo.vue'
import SchoolRating from '@/components/demo/SchoolRating.vue'
import HardwareFacilities from '@/components/demo/HardwareFacilities.vue'
import TeacherStrength from '@/components/demo/TeacherStrength.vue'

// 基础标签配置
const basicTabs = ref([
  { name: '基础数据', component: BasicInfo },
  { name: '学校评情', component: SchoolRating },
  { name: '硬件设施', component: HardwareFacilities },
  { name: '师资力量', component: TeacherStrength }
])

// 带props的标签示例
const propsTabsExample = ref([
  { name: '基础数据', component: BasicInfo },
  { name: '学校评情', component: SchoolRating }
])

// 传递给组件的props
const componentProps = ref({
  customData: '这是传递给组件的数据',
  theme: 'dark'
})

// 事件处理
const onTabChange = (data) => {
  console.log('标签切换:', data)
}

const onTabClick = (data) => {
  console.log('标签点击:', data)
}
</script>

<style scoped lang="scss">
.tabs-el-demo {
  padding: 40px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  
  .demo-header {
    text-align: center;
    margin-bottom: 40px;
    
    h2 {
      color: #fff;
      font-size: 32px;
      margin-bottom: 12px;
      font-weight: 700;
    }
    
    p {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
    }
  }
  
  .demo-section {
    margin-bottom: 40px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    h3 {
      color: #fff;
      font-size: 20px;
      margin-bottom: 20px;
      font-weight: 600;
    }
  }
}
</style>
