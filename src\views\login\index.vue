<template>
  <div class="login-container">
   <el-form
    label-width="100px"
    :model="loginForm"
    style="max-width: 460px"
    ref="loginFormRef"
  >
  <h3 class="title">登录</h3>
    <el-form-item label="用户名">
      <el-input v-model=loginForm.username />
    </el-form-item>
    <el-form-item label="密码">
      <el-input v-model=loginForm.password type="password" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleLogin">登录</el-button>
    </el-form-item>
  </el-form>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const loginFormRef = ref(null);
const loginForm=ref({
    username:'admin',
    password:'123456'
})
const router = useRouter();
const handleLogin = () => {
  if (username.value === 'admin' && password.value === '123456') {
    router.push('/');
  }
}
</script>

<style scoped lang="scss"> 
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;
.login-container{
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
}
</style>