<template>
    <!-- <div>基本信息</div> -->
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    // import TabsSwitch from '@/views/demo/TabsSwitchDemo.vue';
    import TabsSwitch from '@/components/TabsSwitch-el.vue';
    import BasicInfo from '@/components/demo/BasicInfo.vue';
    import SchoolRating from '@/components/demo/SchoolRating.vue';
    import HardwareFacilities from '@/components/demo/HardwareFacilities.vue';
    import TeacherStrength from '@/components/demo/TeacherStrength.vue';
    import { ref } from 'vue';
    const tabs = ref([
        { name: '基础数据', component: BasicInfo },
        { name: '学校评情', component: SchoolRating },
        { name: '硬件设施', component: HardwareFacilities },
        { name: '师资力量', component: TeacherStrength }
    ])
</script>
<style scoped>

</style>
