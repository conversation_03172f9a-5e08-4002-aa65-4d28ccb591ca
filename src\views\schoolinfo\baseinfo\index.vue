<template>
    <!-- <div>基本信息</div> -->
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    // import TabsSwitch from '@/views/demo/TabsSwitchDemo.vue';
    import TabsSwitch from '@/components/TabsSwitch.vue';
    import BasicInfo from '@/views/schoolinfo/baseinfo/component/BaseData.vue';
    import SchoolRating from '@/views/schoolinfo/baseinfo/component/SchoolRating.vue';
    import HardwareFacilities from '@/views/schoolinfo/baseinfo/component/HardwareFacilities.vue';
    import TeachingStaff from '@/views/schoolinfo/baseinfo/component/TeachingStaff.vue';
    import { ref, markRaw } from 'vue';
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '基础数据', component: BasicInfo },
        { name: '学校详情', component: SchoolRating },
        { name: '硬件设施', component: HardwareFacilities },
        { name: '师资力量', component: TeachingStaff }
    ]))
</script>
<style scoped>

</style>
