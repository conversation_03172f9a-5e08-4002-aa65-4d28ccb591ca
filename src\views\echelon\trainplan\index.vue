<template>
    <div style="position: relative;">
        <TabsFilter :tabs="tabs" />
        <el-select v-model="value" placeholder="Select" style="width: 185px" class="select-model">
        <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
        </el-select>
    </div>
</template>
<script setup>
import { ref, markRaw, onMounted } from 'vue';
    import TabsFilter from '@/components/TabsFilter.vue';
    import physicaldata from '@/views/echelon/trainplan/component/physicaldata.vue'; // 导入组件
    import trainingoutline from '@/views/echelon/trainplan/component/trainingoutline.vue'; // 导入组件
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs=ref(markRaw([
        { name: '训练大纲', component: trainingoutline },
        { name: '体能数据', component: physicaldata },
         // 位置信息tab 
    ]))
const value = ref('')
const options = [
  {
    value: 'Option1',
    label: 'Option1',
  },
  {
    value: 'Option2',
    label: 'Option2',
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]
</script>    
<style scoped lang="scss">
    .select-model{
        position: absolute;
        right: 20px;
        top: 0px;
        background-color: transparent;
        :deep(.el-select__wrapper){
            background: transparent;
        }
    }
</style>
