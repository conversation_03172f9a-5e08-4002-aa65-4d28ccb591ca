// 战术视频数据格式示例和扩展功能

// 1. 基础视频数据格式
export const videoDataFormat = {
  id: 1,                                    // 唯一标识
  title: '战术1',                           // 视频标题
  thumbnail: '@/assets/images/video-1.jpg', // 缩略图路径
  videoUrl: '/videos/tactical-1.mp4',       // 视频文件路径
  duration: '05:32',                        // 视频时长
  category: '进攻战术',                     // 视频分类
  uploadDate: '2024-01-15',                 // 上传日期
  description: '战术详细描述...'             // 视频描述
};

// 2. 扩展视频数据格式
export const extendedVideoFormat = {
  id: 1,
  title: '4-3-3阵型进攻战术',
  thumbnail: '@/assets/images/tactical-video-1.jpg',
  videoUrl: '/videos/tactical-1.mp4',
  duration: '05:32',
  category: '进攻战术',
  uploadDate: '2024-01-15',
  description: '详细讲解4-3-3阵型的进攻套路和配合方式',
  
  // 扩展字段
  tags: ['4-3-3', '进攻', '配合', '传球'],     // 标签
  difficulty: 'intermediate',                 // 难度等级: beginner, intermediate, advanced
  viewCount: 1250,                           // 观看次数
  likes: 89,                                 // 点赞数
  coach: '张教练',                           // 讲解教练
  targetAge: 'U15',                          // 适用年龄段
  formation: '4-3-3',                        // 阵型
  tacticalType: 'attack',                    // 战术类型: attack, defense, set_piece, transition
  keyPoints: [                               // 关键要点
    '边路突破配合',
    '中路渗透时机',
    '前场压迫策略'
  ],
  relatedVideos: [2, 5, 8],                 // 相关视频ID
  equipment: ['标志盘', '足球', '战术板'],    // 所需器材
  playerCount: '11v11',                      // 参与人数
  fieldSize: '标准场地',                     // 场地要求
  trainingTime: 45,                          // 建议训练时长(分钟)
  status: 'published',                       // 状态: draft, published, archived
  thumbnail_hover: '@/assets/images/tactical-video-1-hover.jpg' // 悬停缩略图
};

// 3. 视频分类配置
export const videoCategories = [
  { id: 'all', name: '全部', color: '#13F0C6' },
  { id: 'attack', name: '进攻战术', color: '#FF6B6B' },
  { id: 'defense', name: '防守战术', color: '#4ECDC4' },
  { id: 'set_piece', name: '定位球', color: '#45B7D1' },
  { id: 'transition', name: '攻防转换', color: '#96CEB4' },
  { id: 'formation', name: '阵型变换', color: '#FFEAA7' },
  { id: 'individual', name: '个人技术', color: '#DDA0DD' },
  { id: 'teamwork', name: '团队配合', color: '#98D8C8' }
];

// 4. 难度等级配置
export const difficultyLevels = {
  beginner: { name: '初级', color: '#4CAF50', icon: '⭐' },
  intermediate: { name: '中级', color: '#FF9800', icon: '⭐⭐' },
  advanced: { name: '高级', color: '#F44336', icon: '⭐⭐⭐' }
};

// 5. 生成战术视频数据
export function generateTacticalVideos(count = 10) {
  const videos = [];
  const titles = [
    '4-3-3进攻战术', '5-4-1防守体系', '角球战术配合', '快速反击',
    '中场压迫', '边路突破', '定位球防守', '传控战术',
    '高位逼抢', '阵型转换', '个人突破', '团队配合',
    '门前抢点', '后场出球', '中路渗透'
  ];
  
  const categories = ['进攻战术', '防守战术', '定位球', '反击战术', '阵型变换', '传球配合', '压迫战术'];
  const difficulties = ['beginner', 'intermediate', 'advanced'];
  const coaches = ['张教练', '李教练', '王教练', '刘教练'];
  const formations = ['4-3-3', '4-4-2', '3-5-2', '5-3-2', '4-2-3-1'];
  
  for (let i = 0; i < count; i++) {
    const duration = `${String(Math.floor(Math.random() * 8) + 3).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
    const uploadDate = new Date(2024, 0, Math.floor(Math.random() * 30) + 1).toISOString().split('T')[0];
    
    videos.push({
      id: i + 1,
      title: titles[i % titles.length] || `战术${i + 1}`,
      thumbnail: `@/assets/images/tactical-video-${(i % 10) + 1}.jpg`,
      videoUrl: `/videos/tactical-${i + 1}.mp4`,
      duration: duration,
      category: categories[i % categories.length],
      uploadDate: uploadDate,
      description: `这是关于${titles[i % titles.length]}的详细讲解视频，包含实战演练和要点分析。`,
      difficulty: difficulties[i % difficulties.length],
      viewCount: Math.floor(Math.random() * 2000) + 100,
      likes: Math.floor(Math.random() * 200) + 10,
      coach: coaches[i % coaches.length],
      formation: formations[i % formations.length],
      targetAge: ['U9', 'U11', 'U13', 'U15', 'U17'][i % 5]
    });
  }
  
  return videos;
}

// 6. 视频操作方法
export const videoActions = {
  // 播放视频
  playVideo(video) {
    console.log('播放视频:', video.title);
    // 记录观看历史
    this.addToHistory(video);
    // 增加观看次数
    video.viewCount = (video.viewCount || 0) + 1;
    return video;
  },
  
  // 点赞视频
  likeVideo(video) {
    video.likes = (video.likes || 0) + 1;
    console.log(`${video.title} 点赞数: ${video.likes}`);
    return video;
  },
  
  // 添加到收藏
  addToFavorites(video) {
    const favorites = JSON.parse(localStorage.getItem('favoriteVideos') || '[]');
    if (!favorites.find(v => v.id === video.id)) {
      favorites.push(video);
      localStorage.setItem('favoriteVideos', JSON.stringify(favorites));
      console.log('已添加到收藏:', video.title);
    }
    return favorites;
  },
  
  // 添加到观看历史
  addToHistory(video) {
    const history = JSON.parse(localStorage.getItem('videoHistory') || '[]');
    const existingIndex = history.findIndex(v => v.id === video.id);
    
    if (existingIndex > -1) {
      history.splice(existingIndex, 1);
    }
    
    history.unshift({
      ...video,
      watchedAt: new Date().toISOString()
    });
    
    // 只保留最近50个
    if (history.length > 50) {
      history.splice(50);
    }
    
    localStorage.setItem('videoHistory', JSON.stringify(history));
    return history;
  },
  
  // 按分类筛选
  filterByCategory(videos, category) {
    if (category === 'all') return videos;
    return videos.filter(video => video.category === category);
  },
  
  // 按难度筛选
  filterByDifficulty(videos, difficulty) {
    return videos.filter(video => video.difficulty === difficulty);
  },
  
  // 搜索视频
  searchVideos(videos, keyword) {
    const lowerKeyword = keyword.toLowerCase();
    return videos.filter(video => 
      video.title.toLowerCase().includes(lowerKeyword) ||
      video.description.toLowerCase().includes(lowerKeyword) ||
      video.category.toLowerCase().includes(lowerKeyword)
    );
  },
  
  // 排序视频
  sortVideos(videos, sortBy) {
    const sortedVideos = [...videos];
    
    switch (sortBy) {
      case 'newest':
        return sortedVideos.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
      case 'oldest':
        return sortedVideos.sort((a, b) => new Date(a.uploadDate) - new Date(b.uploadDate));
      case 'most_viewed':
        return sortedVideos.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));
      case 'most_liked':
        return sortedVideos.sort((a, b) => (b.likes || 0) - (a.likes || 0));
      case 'duration_asc':
        return sortedVideos.sort((a, b) => this.parseDuration(a.duration) - this.parseDuration(b.duration));
      case 'duration_desc':
        return sortedVideos.sort((a, b) => this.parseDuration(b.duration) - this.parseDuration(a.duration));
      default:
        return sortedVideos;
    }
  },
  
  // 解析时长为秒数
  parseDuration(duration) {
    const [minutes, seconds] = duration.split(':').map(Number);
    return minutes * 60 + seconds;
  }
};

// 7. 使用示例
export const usageExample = {
  // 在组件中使用
  setup() {
    const videoList = ref(generateTacticalVideos(20));
    const filteredVideos = ref([...videoList.value]);
    const currentCategory = ref('all');
    const searchKeyword = ref('');
    
    // 筛选视频
    const filterVideos = () => {
      let result = videoList.value;
      
      // 按分类筛选
      result = videoActions.filterByCategory(result, currentCategory.value);
      
      // 按关键词搜索
      if (searchKeyword.value) {
        result = videoActions.searchVideos(result, searchKeyword.value);
      }
      
      filteredVideos.value = result;
    };
    
    // 播放视频
    const playVideo = (video) => {
      return videoActions.playVideo(video);
    };
    
    return {
      videoList,
      filteredVideos,
      currentCategory,
      searchKeyword,
      filterVideos,
      playVideo
    };
  }
};

// 8. 默认缩略图生成
export const generateDefaultThumbnail = (videoId, title) => {
  // 可以根据视频ID或标题生成默认缩略图
  const colors = ['1a365d', '2d3748', '1a202c', '2a4365', '3c4043'];
  const color = colors[videoId % colors.length];
  return `https://via.placeholder.com/300x200/${color}/ffffff?text=${encodeURIComponent(title)}`;
};

export default {
  videoDataFormat,
  extendedVideoFormat,
  videoCategories,
  difficultyLevels,
  generateTacticalVideos,
  videoActions,
  usageExample,
  generateDefaultThumbnail
};
