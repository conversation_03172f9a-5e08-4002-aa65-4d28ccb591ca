<template>
	<div class="tacticalvideo">
        <ul class="video-grid">
            <li
                v-for="(video, index) in videoList"
                :key="video.id"
                class="video-item"
                @click="playVideo(video)"
            >
                <div class="video-thumbnail">
                    <img :src="video.thumbnail" :alt="video.title" class="thumbnail-img">
                    <div class="play-overlay">
                        <div class="play-button">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                            </svg>
                        </div>
                    </div>
                    <div class="video-duration">{{ video.duration }}</div>
                </div>
                <div class="video-info">
                    <h3 class="video-title">{{ video.title }}</h3>
                    <div class="video-meta">
                        <span class="video-category">{{ video.category }}</span>
                        <span class="video-date">{{ formatDate(video.uploadDate) }}</span>
                    </div>
                </div>
            </li>
        </ul>

        <!-- 视频播放弹窗 -->
        <div v-if="showVideoModal" class="video-modal" @click="closeVideo">
            <div class="modal-content" @click.stop>
                <button class="close-btn" @click="closeVideo">×</button>
                <video
                    v-if="currentVideo"
                    :src="currentVideo.videoUrl"
                    controls
                    autoplay
                    class="video-player"
                >
                    您的浏览器不支持视频播放
                </video>
                <div class="video-details">
                    <h2>{{ currentVideo?.title }}</h2>
                    <p>{{ currentVideo?.description }}</p>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'

// 战术视频数据
const videoList = ref([
    {
        id: 1,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-1.jpg',
        videoUrl: '/videos/tactical-1.mp4',
        duration: '05:32',
        category: '进攻战术',
        uploadDate: '2024-01-15',
        description: '这是一个关于进攻战术的详细讲解视频，包含了多种进攻套路和配合方式。'
    },
    {
        id: 2,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-2.jpg',
        videoUrl: '/videos/tactical-2.mp4',
        duration: '04:18',
        category: '防守战术',
        uploadDate: '2024-01-12',
        description: '防守战术的基本原理和实战应用，帮助球队提高防守效率。'
    },
    {
        id: 3,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-3.jpg',
        videoUrl: '/videos/tactical-3.mp4',
        duration: '06:45',
        category: '定位球',
        uploadDate: '2024-01-10',
        description: '定位球战术的多种变化和执行要点，提高定位球得分率。'
    },
    {
        id: 4,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-4.jpg',
        videoUrl: '/videos/tactical-4.mp4',
        duration: '03:27',
        category: '反击战术',
        uploadDate: '2024-01-08',
        description: '快速反击的时机把握和路线选择，打造高效反击体系。'
    },
    {
        id: 5,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-5.jpg',
        videoUrl: '/videos/tactical-5.mp4',
        duration: '07:12',
        category: '阵型变换',
        uploadDate: '2024-01-05',
        description: '不同阵型之间的转换技巧和适用场景分析。'
    },
    {
        id: 6,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-6.jpg',
        videoUrl: '/videos/tactical-6.mp4',
        duration: '04:55',
        category: '传球配合',
        uploadDate: '2024-01-03',
        description: '短传配合和长传转移的战术运用，提高传球成功率。'
    },
    {
        id: 7,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-7.jpg',
        videoUrl: '/videos/tactical-7.mp4',
        duration: '05:08',
        category: '压迫战术',
        uploadDate: '2024-01-01',
        description: '高位压迫和中场压迫的执行方法和注意事项。'
    },
    {
        id: 8,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-8.jpg',
        videoUrl: '/videos/tactical-8.mp4',
        duration: '06:33',
        category: '边路进攻',
        uploadDate: '2023-12-28',
        description: '边路突破和传中配合的战术要点和实战演练。'
    },
    {
        id: 9,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-9.jpg',
        videoUrl: '/videos/tactical-9.mp4',
        duration: '04:42',
        category: '中路渗透',
        uploadDate: '2023-12-25',
        description: '中路渗透的时机选择和配合方式，撕开对方防线。'
    },
    {
        id: 10,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-10.jpg',
        videoUrl: '/videos/tactical-10.mp4',
        duration: '05:21',
        category: '整体配合',
        uploadDate: '2023-12-22',
        description: '全队整体配合的节奏控制和空间利用技巧。'
    }
    ,
    {
        id: 11,
        title: '战术1',
        thumbnail: '@/assets/images/tactical-video-10.jpg',
        videoUrl: '/videos/tactical-10.mp4',
        duration: '05:21',
        category: '整体配合',
        uploadDate: '2023-12-22',
        description: '全队整体配合的节奏控制和空间利用技巧。'
    }
]);

// 视频播放相关状态
const showVideoModal = ref(false);
const currentVideo = ref(null);

// 播放视频
const playVideo = (video) => {
    console.log('播放视频:', video.title);
    currentVideo.value = video;
    showVideoModal.value = true;
};

// 关闭视频
const closeVideo = () => {
    showVideoModal.value = false;
    currentVideo.value = null;
};

// 格式化日期
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
};

// 生成默认缩略图（如果图片不存在）
const generateThumbnail = (videoId) => {
    // 这里可以返回一个默认的战术图片或者根据ID生成不同的图片
    return `https://via.placeholder.com/300x200/1a365d/ffffff?text=战术${videoId}`;
};

// 组件挂载时的初始化
onMounted(() => {
    console.log('战术视频组件已加载，共', videoList.value.length, '个视频');

    // 如果需要从API加载数据，可以在这里调用
    // loadTacticalVideos();
});

// 模拟API加载数据
const loadTacticalVideos = async () => {
    try {
        // 这里可以调用实际的API
        console.log('加载战术视频数据...');
        // const response = await api.getTacticalVideos();
        // videoList.value = response.data;
    } catch (error) {
        console.error('加载战术视频失败:', error);
    }
};
</script>
<style scoped lang="scss">
    .tacticalvideo{
        padding: 30px;
        color: #fff;
        padding-top: 0px;
        overflow: auto;
        height: 500px;
        .video-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            list-style: none;
            padding: 0;
            margin: 0;
           overflow-y: auto;
           height: 100%;
            .video-item {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                overflow: hidden;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.1);

                &:hover {
                    transform: translateY(-5px);
                    background: rgba(255, 255, 255, 0.08);
                    border-color: rgba(19, 240, 198, 0.3);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);

                    .play-overlay {
                        opacity: 1;
                    }

                    .thumbnail-img {
                        transform: scale(1.05);
                    }
                }

                .video-thumbnail {
                    position: relative;
                    width: 100%;
                    height: 140px;
                    overflow: hidden;
                    background: #1a365d;

                    .thumbnail-img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }

                    .play-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        opacity: 0;
                        transition: opacity 0.3s ease;

                        .play-button {
                            width: 50px;
                            height: 50px;
                            background: rgba(19, 240, 198, 0.9);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #fff;
                            font-size: 20px;
                            transform: scale(0.8);
                            transition: transform 0.3s ease;

                            &:hover {
                                transform: scale(1);
                                background: #13F0C6;
                            }
                        }
                    }

                    .video-duration {
                        position: absolute;
                        bottom: 8px;
                        right: 8px;
                        background: rgba(0, 0, 0, 0.8);
                        color: #fff;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                    }
                }

                .video-info {
                    padding: 15px;

                    .video-title {
                        font-size: 16px;
                        font-weight: 500;
                        color: #fff;
                        margin: 0 0 8px 0;
                        line-height: 1.3;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .video-meta {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.6);

                        .video-category {
                            background: rgba(19, 240, 198, 0.2);
                            color: #13F0C6;
                            padding: 2px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                        }

                        .video-date {
                            font-size: 11px;
                        }
                    }
                }
            }
        }

        // 视频播放弹窗样式
        .video-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;

            .modal-content {
                position: relative;
                max-width: 90vw;
                max-height: 90vh;
                background: #1a1a1a;
                border-radius: 12px;
                overflow: hidden;

                .close-btn {
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    color: #fff;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    font-size: 24px;
                    cursor: pointer;
                    z-index: 1001;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: background 0.3s ease;

                    &:hover {
                        background: rgba(255, 255, 255, 0.3);
                    }
                }

                .video-player {
                    width: 100%;
                    max-width: 800px;
                    height: auto;
                }

                .video-details {
                    padding: 20px;

                    h2 {
                        color: #13F0C6;
                        margin: 0 0 10px 0;
                        font-size: 20px;
                    }

                    p {
                        color: rgba(255, 255, 255, 0.8);
                        line-height: 1.5;
                        margin: 0;
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1400px) {
        .tacticalvideo .video-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 1200px) {
        .tacticalvideo .video-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 768px) {
        .tacticalvideo {
            padding: 15px;

            .video-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;

                .video-item {
                    .video-thumbnail {
                        height: 120px;
                    }

                    .video-info {
                        padding: 12px;

                        .video-title {
                            font-size: 14px;
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 480px) {
        .tacticalvideo .video-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
