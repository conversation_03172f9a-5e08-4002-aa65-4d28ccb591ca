<template>
	<div class="normalformation">
        <ul class="formation-list">
            <li class="formation-item">
                <img src="@/assets/images/board.png" alt="" srcset="">
                <p>阵型一</p>
            </li>
            <li class="formation-item">
                <img src="@/assets/images/board.png" alt="" srcset="">
                <p>阵型二</p>
            </li>
            <li class="formation-item">
                <img src="@/assets/images/board.png" alt="" srcset="">
                <p>阵型三</p>
            </li>
        </ul>
    </div>
</template>
<script setup>

</script>
<style scoped lang="scss">
    .normalformation{
        padding: 30px;
        color: #fff;
        padding-top: 0px;
        overflow-y: auto;
        .formation-list{
            display: flex;
            flex-wrap: wrap;
            gap: 60px;
           .formation-item{
            
            img{
                width: 426px;
                height: 285px;
            }
            p{
                font-size: 16px;
                color: #fff;
                margin-top: 4px;
            }
        } 
        }
        
    }
</style>
