<template>
  <div class="hardware-facilities">
    <h3>硬件设施</h3>
    <div class="facilities-grid">
      <div class="facility-card" v-for="facility in facilities" :key="facility.id">
        <div class="facility-icon">
          <i :class="facility.icon"></i>
        </div>
        <div class="facility-info">
          <h4>{{ facility.name }}</h4>
          <p class="count">{{ facility.count }}</p>
          <p class="description">{{ facility.description }}</p>
        </div>
        <div class="facility-status" :class="facility.status">
          {{ facility.statusText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'HardwareFacilities'
})

const facilities = ref([
  {
    id: 1,
    name: '足球场',
    count: '89个',
    description: '标准11人制足球场',
    icon: 'fas fa-futbol',
    status: 'excellent',
    statusText: '优秀'
  },
  {
    id: 2,
    name: '训练器材',
    count: '1,256套',
    description: '专业足球训练设备',
    icon: 'fas fa-dumbbell',
    status: 'good',
    statusText: '良好'
  },
  {
    id: 3,
    name: '更衣室',
    count: '156间',
    description: '现代化更衣设施',
    icon: 'fas fa-door-open',
    status: 'excellent',
    statusText: '优秀'
  },
  {
    id: 4,
    name: '医疗室',
    count: '78间',
    description: '运动医疗保障',
    icon: 'fas fa-first-aid',
    status: 'good',
    statusText: '良好'
  }
])
</script>

<style scoped lang="scss">
.hardware-facilities {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  
  h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    
    .facility-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }
      
      .facility-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        border-radius: 12px;
        
        i {
          font-size: 24px;
          color: #fff;
        }
      }
      
      .facility-info {
        flex: 1;
        
        h4 {
          color: #fff;
          font-size: 16px;
          margin-bottom: 4px;
        }
        
        .count {
          color: #00d4ff;
          font-weight: 600;
          font-size: 18px;
          margin-bottom: 4px;
        }
        
        .description {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
        }
      }
      
      .facility-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        
        &.excellent {
          background: rgba(0, 255, 127, 0.2);
          color: #00ff7f;
          border: 1px solid #00ff7f;
        }
        
        &.good {
          background: rgba(255, 215, 0, 0.2);
          color: #ffd700;
          border: 1px solid #ffd700;
        }
      }
    }
  }
}
</style>
