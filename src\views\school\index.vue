<template>
  <div class="conatiner">
    <!-- <div class="school-title">
        <span class="title-word">南山特色足球学校</span>
        <span class="title-beautiful">Nanshan Characteristic Football School</span>
    </div> -->
    <TitleInfo title="南山特色足球学校" beautiful="Nanshan Characteristic Football School" />
    <div class="school-content">
        <div class="school-tag">
            <el-select
                v-model="value"
                class="m-2"
                placeholder="Select"
                style="width: 240px"
            >
                <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                />
            </el-select>
        </div>
        <div class="school-container">
            <div>
                <ul class="school-list">
                    <li class="school-item" v-for="item in schoolList" :key="item.name">
                        <span class="school-address">{{item.address}}</span>
                        <div class="school-img"><img :src="item.img" alt="" srcset=""></div>
                        <div class="school-name">{{item.name}}</div>
                        <template v-if="item.special.length>0">
                            <div class="school-special" v-for="special in item.special" :key="special"><span>{{special}}</span></div>
                        </template>
                        <div class="school-honor">{{item.honor}}</div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
  </div>
</template>
<script setup>
    import { ref } from 'vue';
    import TitleInfo from '@/components/TitleInfo.vue';
    const value = ref('');
    const options = [
        {
            value: 'Option1',
            label: 'Option1',
        },
        {
            value: 'Option2',
            label: 'Option2',
        },
        {
            value: 'Option3',
            label: 'Option3',
        },
        {
            value: 'Option4',
            label: 'Option4',
        },
        {
            value: 'Option5',
            label: 'Option5',
        }
    ]
    const schoolList = ref([
        {
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        },
        {
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        },
        {
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        },{
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        },{
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        },{
            address: '南山',
            img: '/src/assets/images/school_icon.png',
            name: '南外文化学校',
            special: ['全国青少年校园足球特色学校', '区级青训基地'],
            honor: '深圳市中小学生校园足球比赛冠军'
        }
    ])
</script>
<style scoped lang="scss">
    .conatiner{
        width: 100%;
        height: 100%;
        // .school-title{
        //     margin-bottom: 21px;
        //     .title-word{
        //         margin-left: 7px;
        //         font-family: YouSheBiaoTiHei;
        //         font-size: 33px;
        //         -webkit-text-fill-color: transparent;
        //         background: -webkit-linear-gradient(top, rgb(255, 255, 255), #90DEFF) text;
        //     }
        //     .title-beautiful{
        //         margin-left: 19px;
        //         font-size: 15px;
        //         color: #5484A0;
        //     }
        // }
        .school-content{
            .school-tag{
                width: 100%;
                overflow: hidden;
                .el-select{
                    float: right;
                    margin-top: 10px;
                    margin-bottom: 21px;
                }    
            }
            .school-container{
                width: 1495px;
                height: 619px;
                background: rgba(1,31,81,0.7);
                border-radius: 10px;
                margin: 0 auto;
                .school-list{
                    overflow-y: auto;
                    scrollbar-width: none;
                    padding: 28px 25px 28px 25px;
                    &::-webkit-scrollbar{
                        display: none;
                    }
                    
                    .school-item{
                        &:nth-child(6n){
                        margin-right: 0;
                    }
                        overflow: hidden;
                        float: left;
                        margin-right: 58px;
                        position: relative;
                        box-sizing: border-box;
                        width: 190px;
                        height: 277px;
                        background-image: url(@/assets/images/scorebg.png);
                        background-size: 100% 100%;
                        .school-address{
                            position: absolute;
                            border: 1px solid #13F0C6;
                            top: 14px;
                            right:8px;
                            font-size: 7px;
                            color: #13F0C6;
                            border-radius: 2px;
                            padding: 1px 6px;
                        }
                        .school-img{
                            width: 90px;
                            height: 90px;
                            margin: 0 auto;
                            margin-top: 26px;
                            img{
                                
                                width: 100%;
                                height: 100%;
                            }
                        }
                        .school-name{
                            color: #fff;
                            font-size: 20px;
                            font-weight: 500;
                            margin-top: 15px;
                            text-align: center;
                            margin-bottom: 15px;
                        }
                        .school-special{
                            text-align: center;
                            height: auto;
                            margin-bottom: 8px;
                            span{
                                
                                font-size: 9px;
                                padding: 4px 10px;
                                color: #0EDEF1;
                                border: 1px solid #0EDEF1;
                                border-radius: 13px;
                            }  
                        }
                        .school-honor{
                            position: absolute;
                            bottom: 22px;
                            font-size: 9px;
                            width: 100%;
                            color: #F1B10E;
                            text-align: center;
                        }
                    }  
                }
                
            }
        }

    }

</style>