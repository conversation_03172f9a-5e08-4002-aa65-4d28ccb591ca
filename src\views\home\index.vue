
<template>
    <div class="container" v-if="isShow">
        <div class="container-content">
            <div @click="handleOpen" class="close"><el-icon size="40" color="#fff"><Close  /></el-icon></div>
            <RouterView />
        </div>
        
    </div>
  <el-button type="primary" @click="handleOpen">打开</el-button>
  <el-select
    v-model="value"
    class="m-2"
    placeholder="Select"
    size="large"
    style="width: 240px"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script setup>
import { ref } from 'vue'
const isShow = ref(false);
const handleOpen = () => {
    isShow.value = !isShow.value;
}

const value = ref('')

const options = [
  {
    value: 'Option1',
    label: 'Option1',
  },
  {
    value: 'Option2',
    label: 'Option2',
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]
</script>
<style scoped lang="scss">
    .container{
        position: fixed;
        display: block;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        z-index: 1;
        background-color: rgba(0,0,0,0.6);
        .container-content{
            position: absolute;
            box-sizing: border-box;
            top: 10%; 
            left: 10%;
            // transform: translate(-50%, -50%);
            width: 1549px;
            height: 800px;
            background-image: url(@/assets/images/schoolbg.png);
            background-size: 100% 100%;
            padding: 26px 29px 25px 25px;
            .close{
                position: absolute;
                cursor: pointer;
                right: 23px;
                top: 26px;
            }
        }
    }
</style>