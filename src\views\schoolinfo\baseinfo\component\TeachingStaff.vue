<template>
	<div class="teaching-staff">
        <div class="section">
            <div class="section-title">
                <div class="title-line"></div>
                <div class="title-text">师资力量</div>
            </div>
            <ul class="teacher-list">
                <li
                    v-for="(teacher, index) in teacherList"
                    :key="index"
                    class="teacher-item"
                >
                    <div class="teacher-avatar">
                        <img :src="teacher.avatar" :alt="teacher.name" class="avatar-img">
                    </div>
                    <div class="teacher-name">{{ teacher.name }}</div>
                    <div class="teacher-info">{{ teacher.age }}岁 | {{ teacher.position }} | {{ teacher.level }}</div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue'
import teacherIcon from '@/assets/images/coach-01.png'

defineOptions({
    name: 'teaching-staff'
})

const teacherList = ref([
        {
            avatar: teacherIcon,
            name: '斯洛特',
            age: 46,
            position: '主教练',
            level: 'D级'
        },
        {
            avatar: teacherIcon,
            name: '约翰福音',
            age: 41,
            position: '助理教练',
            level: 'A级'
        },
        {
            avatar: teacherIcon,
            name: '斯洛特',
            age: 46,
            position: '主教练',
            level: 'D级'
        },
        {
            avatar: teacherIcon,
            name: '约翰福音',
            age: 41,
            position: '助理教练',
            level: 'A级'
        }
    ])
</script>

<style src="@/styles/styles.scss"></style>
<style scoped lang="scss">
.teaching-staff {
    padding: 30px;
    padding-top: 0;

    .teacher-list {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        list-style: none;
        padding: 0;
        margin: 0;

        .teacher-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;

            .teacher-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                overflow: hidden;
                margin-bottom: 12px;
                border: 2px solid rgba(255, 255, 255, 0.2);

                .avatar-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .teacher-name {
                font-size: 18px;
                font-weight: 500;
                color: #ffffff;
                margin-bottom: 6px;
                text-align: center;
            }

            .teacher-info {
                font-size: 15px;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.6);
                text-align: center;
                line-height: 1.4;
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 15px;

        .teacher-list {
            gap: 20px;
            justify-content: center;

            .teacher-item {
                min-width: 100px;

                .teacher-avatar {
                    width: 45px;
                    height: 45px;
                }

                .teacher-name {
                    font-size: 14px;
                }

                .teacher-info {
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
