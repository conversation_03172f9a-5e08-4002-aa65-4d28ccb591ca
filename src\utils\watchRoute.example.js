// 使用示例文件
import { onMounted, onUnmounted } from 'vue'
import { watchRoute, watchRoutePath, watchRouteParams, watchFullRoute } from './watchRoute.js'

export function useRouteWatcher() {
    let stopWatchers = []

    onMounted(() => {
        // 示例1: 监听查询参数变化
        const stopQueryWatcher = watchRoute((newQuery, oldQuery) => {
            console.log('查询参数变化:', oldQuery, '->', newQuery)
            
            // 处理特定的查询参数
            if (newQuery.key !== oldQuery?.key) {
                console.log('key参数变化:', newQuery.key)
                handleKeyChange(newQuery.key)
            }
            
            if (newQuery.tab !== oldQuery?.tab) {
                console.log('tab参数变化:', newQuery.tab)
                handleTabChange(newQuery.tab)
            }
        })
        stopWatchers.push(stopQueryWatcher)

        // 示例2: 监听路径变化
        const stopPathWatcher = watchRoutePath((newPath, oldPath) => {
            console.log('路径变化:', oldPath, '->', newPath)
            handlePathChange(newPath)
        })
        stopWatchers.push(stopPathWatcher)

        // 示例3: 监听路由参数变化
        const stopParamsWatcher = watchRouteParams((newParams, oldParams) => {
            console.log('路由参数变化:', oldParams, '->', newParams)
            
            if (newParams.id !== oldParams?.id) {
                console.log('ID参数变化:', newParams.id)
                fetchDataById(newParams.id)
            }
        })
        stopWatchers.push(stopParamsWatcher)

        // 示例4: 监听完整路由变化
        const stopFullWatcher = watchFullRoute((newRoute, oldRoute) => {
            console.log('完整路由变化:', oldRoute?.fullPath, '->', newRoute.fullPath)
            
            // 页面切换时的通用处理
            if (newRoute.name !== oldRoute?.name) {
                handlePageChange(newRoute.name, oldRoute?.name)
            }
        })
        stopWatchers.push(stopFullWatcher)
    })

    // 组件卸载时停止所有监听
    onUnmounted(() => {
        stopWatchers.forEach(stop => stop())
        stopWatchers = []
    })

    // 处理函数
    function handleKeyChange(key) {
        console.log('处理key变化:', key)
        // 实际的业务逻辑
    }

    function handleTabChange(tab) {
        console.log('处理tab变化:', tab)
        // 实际的业务逻辑
    }

    function handlePathChange(path) {
        console.log('处理路径变化:', path)
        // 实际的业务逻辑
    }

    function fetchDataById(id) {
        console.log('根据ID获取数据:', id)
        // 实际的数据获取逻辑
    }

    function handlePageChange(newPage, oldPage) {
        console.log('页面切换:', oldPage, '->', newPage)
        // 实际的页面切换逻辑
    }

    return {
        // 可以返回一些方法供外部使用
        stopAllWatchers: () => {
            stopWatchers.forEach(stop => stop())
            stopWatchers = []
        }
    }
}
