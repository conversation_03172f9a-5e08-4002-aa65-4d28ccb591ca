<template>
  <div class="base-data">
    <!-- 指标部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">指标</span>
      </div>
      <div class="indicators-grid">
        <div class="indicator-item">
          <div class="indicator-icon hexagon">
            <img src="@/assets/images/footballfield-icon.png" alt="足球场">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">1</div>
            <div class="indicator-label">足球场</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon hexagon">
            <img src="@/assets/images/teacher-icon.png" alt="足球教师人数">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">183</div>
            <div class="indicator-label">足球教师人数</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon hexagon">
            <img src="@/assets/images/champion-icon.png" alt="年度参赛">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">4</div>
            <div class="indicator-label">年度参赛</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon hexagon">
            <img src="@/assets/images/talent-transfer-icon.png" alt="人才输送">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">666</div>
            <div class="indicator-label">人才输送</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 荣誉部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">荣誉</span>
      </div>
      <div class="honors-grid">
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">南山校足球冠军</div>
            <div class="honor-years">
              <span class="year-tag">2020/05</span>
              <span class="year-tag">2022/05</span>
            </div>
          </div>
        </div>
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">深圳青少年小学足球赛冠军 (3次)</div>
            <div class="honor-years">
              <span class="year-tag">2022/05</span>
              <span class="year-tag">2024/05</span>
            </div>
          </div>
        </div>
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">南山区少年儿童足球赛标赛冠军 (2次)</div>
            <div class="honor-years">
              <span class="year-tag">2020/05</span>
              <span class="year-tag">2022/05</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 校内活动部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">校内活动</span>
      </div>
      <ul class="activities-list">
        <li class="activity-item">
          <img src="@/assets/images/school-01.png" alt="校内活动1" class="activity-image">
        </li>
        <li class="activity-item">
          <img src="@/assets/images/school-02.png" alt="校内活动2" class="activity-image">
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'BaseData'
})
</script>

<style scoped lang="scss">
.base-data {
  padding: 30px;
  color: #fff;

  .section {
    margin-bottom: 40px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 25px;

      .title-line {
        width: 4px;
        height: 20px;
        background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
        border-radius: 2px;
        margin-right: 12px;
      }

      .title-text {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
      }
    }
  }

  // 指标部分样式
  .indicators-grid {
    display: flex;
    gap: 30px;
    justify-content: flex-start;

    .indicator-item {
      display: flex;
      align-items: center;
      gap: 15px;

      .indicator-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &.hexagon {
          background: linear-gradient(135deg, #1a4d72, #2a5d8a);
          clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
          border: 2px solid #4a90c2;
        }

        img {
          width: 24px;
          height: 24px;
          object-fit: contain;
          filter: brightness(0) invert(1);
        }
      }

      .indicator-content {
        .indicator-number {
          font-size: 32px;
          font-weight: 700;
          color: #ffffff;
          line-height: 1;
          margin-bottom: 2px;
        }

        .indicator-label {
          font-size: 14px;
          color: #a0b4c7;
          line-height: 1;
          white-space: nowrap;
        }
      }
    }
  }

  // 荣誉部分样式
  .honors-grid {
    display: flex;
    gap: 30px;

    .honor-item {
      display: flex;
      align-items: flex-start;
      gap: 15px;
      flex: 1;

      .trophy-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        img {
          width: 50px;
          height: 50px;
          object-fit: contain;
          filter: sepia(1) saturate(3) hue-rotate(35deg) brightness(1.2);
        }
      }

      .honor-content {
        flex: 1;

        .honor-title {
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          line-height: 1.3;
          margin-bottom: 8px;
        }

        .honor-years {
          display: flex;
          gap: 6px;
          flex-wrap: wrap;

          .year-tag {
            padding: 2px 8px;
            background: #ff8c00;
            color: #ffffff;
            font-size: 11px;
            font-weight: 500;
            border-radius: 10px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 校内活动部分样式
  .activities-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 15px;

    .activity-item {
      border-radius: 8px;
      overflow: hidden;

      .activity-image {
        width: auto;
        height: 118px;
        object-fit: cover;
        display: block;
        border-radius: 8px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .indicators-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .honors-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .indicators-grid,
    .honors-grid {
      grid-template-columns: 1fr;
    }

    .activities-list {
      flex-direction: column;
    }

    .indicator-item,
    .honor-item {
      padding: 15px;
    }
  }
}
</style>
