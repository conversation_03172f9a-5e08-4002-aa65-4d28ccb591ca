<template>
  <div class="base-data">
    <!-- 指标部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">指标</span>
      </div>
      <div class="indicators-grid">
        <div class="indicator-item">
          <div class="indicator-icon">
            <img src="@/assets/images/footballfield-icon.png" alt="足球场">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">1</div>
            <div class="indicator-label">足球场</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon">
            <img src="@/assets/images/teacher-icon.png" alt="足球教师人数">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">183</div>
            <div class="indicator-label">足球教师人数</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon">
            <img src="@/assets/images/champion-icon.png" alt="年度参赛">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">4</div>
            <div class="indicator-label">年度参赛</div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-icon">
            <img src="@/assets/images/talent-transfer-icon.png" alt="人才输送">
          </div>
          <div class="indicator-content">
            <div class="indicator-number">666</div>
            <div class="indicator-label">人才输送</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 荣誉部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">荣誉</span>
      </div>
      <div class="honors-grid">
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">南山校足球冠军</div>
            <div class="honor-years">
              <span class="year-tag">2020/05</span>
              <span class="year-tag">2022/05</span>
              <span class="year-tag">2022/05</span>
            </div>
          </div>
        </div>
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">深圳青少年小学足球赛冠军 (3次)</div>
            <div class="honor-years">
              <span class="year-tag">2022/05</span>
              <span class="year-tag">2024/05</span>
            </div>
          </div>
        </div>
        <div class="honor-item">
          <div class="trophy-icon">
            <img src="@/assets/images/championship-trophy.png" alt="奖杯">
          </div>
          <div class="honor-content">
            <div class="honor-title">南山区少年儿童足球赛标赛冠军 (2次)</div>
            <div class="honor-years">
              <span class="year-tag">2020/05</span>
              <span class="year-tag">2022/05</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 校内活动部分 -->
    <div class="section">
      <div class="section-title">
        <div class="title-line"></div>
        <span class="title-text">校内活动</span>
      </div>
      <ul class="activities-list">
        <li class="activity-item">
          <img src="@/assets/images/school-02.png" alt="校内活动2" class="activity-image">
        </li>
        <li class="activity-item">
          <img src="@/assets/images/school-02.png" alt="校内活动2" class="activity-image">
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'BaseData'
})
</script>

<style src="./styles.css"></style>
<style scoped lang="scss">
.base-data {
  padding: 30px;
  color: #fff;
  padding-top: 0px;
overflow-y: auto;
  .section {
    margin-bottom: 25px;
  }

  // 指标部分样式
  .indicators-grid {
    display: flex;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;

    .indicator-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      .indicator-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        border-radius: 50%;

        img {
          width: 55px;
          height: 55px;
          object-fit: contain;
        }
      }

      .indicator-content {
        .indicator-number {
          font-size: 24px;
          font-weight: 700;
          color: #fff;
          line-height: 1;
          margin-bottom: 4px;
        }

        .indicator-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1;
        }
      }
    }
  }

  // 荣誉部分样式
  .honors-grid {
    display: flex;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .honor-item {
      display: flex;
      align-items: flex-start;
      padding: 20px;
      .trophy-icon {
        width: 60px;
        height: 60px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        img {
          width: 50px;
          height: 50px;
          object-fit: contain;
        }
      }

      .honor-content {
        flex: 1;

        .honor-title {
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          line-height: 1.4;
          margin-bottom: 12px;
        }

        .honor-years {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .year-tag {
            padding: 2px 10px;
            background: #D1751C;
            color: #fff;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 校内活动部分样式
  .activities-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow: hidden; // 清除浮动

    .activity-item {
      float: left;
      margin-right: 40px;
      border-radius: 12px;
      overflow: hidden;

      &:last-child {
        margin-right: 0; // 最后一个元素不需要右间隔
      }

      .activity-image {
        width: auto;
        height: 118px;
        object-fit: cover;
        display: block;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .indicators-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .honors-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .indicators-grid,
    .honors-grid {
      grid-template-columns: 1fr;
    }

    .activities-list {
      .activity-item {
        float: none;
        display: block;
        margin-right: 0;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .indicator-item,
    .honor-item {
      padding: 15px;
    }
  }
}
</style>
