<template>
	<div class="teaminfo">
        <!-- 指标部分 -->
        <div class="section">
            <div class="section-title">
                <div class="title-line"></div>
                <span class="title-text">指标</span>
            </div>
            <div class="indicators-grid">
                <div class="indicator-item">
                <div class="indicator-icon">
                    <img src="@/assets/images/footballfield-icon.png" alt="足球场">
                </div>
                <div class="indicator-content">
                    <div class="indicator-number">1</div>
                    <div class="indicator-label">足球场</div>
                </div>
                </div>
                <div class="indicator-item">
                <div class="indicator-icon">
                    <img src="@/assets/images/teacher-icon.png" alt="足球教师人数">
                </div>
                <div class="indicator-content">
                    <div class="indicator-number">183</div>
                    <div class="indicator-label">足球教师人数</div>
                </div>
                </div>
                <div class="indicator-item">
                <div class="indicator-icon">
                    <img src="@/assets/images/champion-icon.png" alt="年度参赛">
                </div>
                <div class="indicator-content">
                    <div class="indicator-number">4</div>
                    <div class="indicator-label">年度参赛</div>
                </div>
                </div>
                <div class="indicator-item">
                <div class="indicator-icon">
                    <img src="@/assets/images/talent-transfer-icon.png" alt="人才输送">
                </div>
                <div class="indicator-content">
                    <div class="indicator-number">666</div>
                    <div class="indicator-label">人才输送</div>
                </div>
                </div>
            </div>
        </div>
        <div class="section">
                <div class="section-title">
                    <div class="title-line"></div>
                    <div class="title-text">师资力量</div>
                </div>
                <ul class="teacher-list">
                    <li
                        v-for="(teacher, index) in teacherList"
                        :key="index"
                        class="teacher-item"
                    >
                        <div class="teacher-avatar">
                            <img :src="teacher.avatar" :alt="teacher.name" class="avatar-img">
                        </div>
                        <div class="teacher-name">{{ teacher.name }}</div>
                        <div class="teacher-info">{{ teacher.age }}岁 | {{ teacher.position }} | {{ teacher.level }}</div>
                    </li>
                </ul>
        </div>
        <div class="section">
            <div class="section-title">
                    <div class="title-line"></div>
                    <div class="title-text">队服</div>
                </div>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue'
import teacherIcon from '@/assets/images/coach-01.png'
const teacherList = ref([
        {
            avatar: teacherIcon,
            name: '斯洛特',
            age: 46,
            position: '主教练',
            level: 'D级'
        },
        {
            avatar: teacherIcon,
            name: '约翰福音',
            age: 41,
            position: '助理教练',
            level: 'A级'
        },
        {
            avatar: teacherIcon,
            name: '斯洛特',
            age: 46,
            position: '主教练',
            level: 'D级'
        },
        {
            avatar: teacherIcon,
            name: '约翰福音',
            age: 41,
            position: '助理教练',
            level: 'A级'
        }
    ])
</script>
<style src="@/styles/styles.scss"></style>
<style scoped lang="scss">
    // 指标部分样式
    .teaminfo{
        padding: 30px;
        color: #fff;
        padding-top: 0px;
        overflow-y: auto;
        .section {
            margin-bottom: 25px;
        }
    }
  .indicators-grid {
    display: flex;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;

    .indicator-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      .indicator-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        border-radius: 50%;

        img {
          width: 55px;
          height: 55px;
          object-fit: contain;
        }
      }

      .indicator-content {
        .indicator-number {
          font-size: 24px;
          font-weight: 700;
          color: #fff;
          line-height: 1;
          margin-bottom: 4px;
        }

        .indicator-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1;
        }
      }
    }
  }


    .teacher-list {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        list-style: none;
        padding: 0;
        margin: 0;

        .teacher-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;

            .teacher-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                overflow: hidden;
                margin-bottom: 12px;
                border: 2px solid rgba(255, 255, 255, 0.2);

                .avatar-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .teacher-name {
                font-size: 18px;
                font-weight: 500;
                color: #ffffff;
                margin-bottom: 6px;
                text-align: center;
            }

            .teacher-info {
                font-size: 15px;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.6);
                text-align: center;
                line-height: 1.4;
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 15px;

        .teacher-list {
            gap: 20px;
            justify-content: center;

            .teacher-item {
                min-width: 100px;

                .teacher-avatar {
                    width: 45px;
                    height: 45px;
                }

                .teacher-name {
                    font-size: 14px;
                }

                .teacher-info {
                    font-size: 12px;
                }
            }
        }
    }
</style>
