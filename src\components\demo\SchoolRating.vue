<template>
  <div class="school-rating">
    <h3>学校评情</h3>
    <div class="rating-list">
      <div class="rating-item" v-for="school in schools" :key="school.id">
        <div class="school-info">
          <span class="school-name">{{ school.name }}</span>
          <span class="school-type">{{ school.type }}</span>
        </div>
        <div class="rating-score">
          <span class="score">{{ school.score }}</span>
          <div class="stars">
            <span v-for="i in 5" :key="i" class="star" :class="{ active: i <= school.stars }">★</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'SchoolRating'
})

const schools = ref([
  { id: 1, name: '南外文化学校', type: '特色学校', score: 95, stars: 5 },
  { id: 2, name: '南山实验学校', type: '示范学校', score: 92, stars: 5 },
  { id: 3, name: '南头中学', type: '重点学校', score: 88, stars: 4 },
  { id: 4, name: '育才中学', type: '优质学校', score: 85, stars: 4 }
])
</script>

<style scoped lang="scss">
.school-rating {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  
  h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .rating-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .rating-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
      }
      
      .school-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .school-name {
          color: #fff;
          font-weight: 600;
          font-size: 16px;
        }
        
        .school-type {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
        }
      }
      
      .rating-score {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;
        
        .score {
          color: #00d4ff;
          font-weight: 700;
          font-size: 18px;
        }
        
        .stars {
          display: flex;
          gap: 2px;
          
          .star {
            color: rgba(255, 255, 255, 0.3);
            font-size: 14px;
            
            &.active {
              color: #ffd700;
            }
          }
        }
      }
    }
  }
}
</style>
