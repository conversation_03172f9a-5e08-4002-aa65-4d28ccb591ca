<template>
    <div class="tabs-switch">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" >
            <!-- <el-tab-pane label="User" name="first">User</el-tab-pane>
            <el-tab-pane label="Config" name="second">Config</el-tab-pane>
            <el-tab-pane label="Role" name="third">Role</el-tab-pane>
            <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            <el-tab-pane v-for="(item, index) in props.tabs" :key="index" :label="item.name" :name="item.name">
                <component
                    :is="item.component"
                    v-if="item.component"
                    v-bind="componentProps"
                />
                <div v-else class="empty-content">
                    {{ item.name }} 内容
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
	
</template>
<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
// import { ElTabs, ElTabPane } from 'element-plus'

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
        default: () => []
    },
    // 默认激活的标签
    defaultActive: {
        type: [String, Number],
        default: 0
    },
    // 传递给组件的props
    componentProps: {
        type: Object,
        default: () => ({})
    }
})

// 定义事件
// const emit = defineEmits(['change', 'tab-click'])

const activeName = ref('');

// 初始化激活的标签
const initActiveTab = () => {
    if (props.tabs && props.tabs.length > 0) {
        if (typeof props.defaultActive === 'number') {
            // 如果是数字索引
            const index = Math.min(props.defaultActive, props.tabs.length - 1);
            activeName.value = props.tabs[index].name;
        } else {
            // 如果是字符串名称
            const found = props.tabs.find(tab => tab.name === props.defaultActive);
            activeName.value = found ? found.name : props.tabs[0].name;
        }
    }
};

// 初始化
initActiveTab();

// 标签点击处理
const handleClick = (tab) => {
    const tabIndex = props.tabs.findIndex(t => t.name === tab.props.name);
    const tabData = props.tabs[tabIndex];
    console.log('切换到标签:', tabData.name);
    console.log(route.path);
    router.push({
        path: route.path,
        query: { key: tabData.name } // 数据会显示在URL中：/user/123?key=好
    });
}
</script>

<style scoped lang="scss">
    :deep(.el-tabs__header){
        margin-top: 8px;
    }
    .demo-tabs > .el-tabs__content {
        padding: 32px;
        color: #6b778c;
        font-size: 32px;
        font-weight: 600;
    }
    :deep(.el-tabs__nav){
        .el-tabs__item{
            color: #fff;
            font-size: 20px;
            padding: 0 40px;
            height: 42px;
        }
        .is-active{
            color: #2DD8B7;
        }
        .el-tabs__active-bar{
            background-color: #2DD8B7;
            height: 4px;
            border-radius: 8px;
            bottom: 1px;
        }
    }
    :deep(.el-tabs__nav-wrap:after){
        background-color: rgba(8, 139, 255, 0.2);
    }
    :deep(.el-tabs__nav){
        margin-left: 60px;
    }

    .empty-content {
        padding: 40px 20px;
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin: 20px 0;
    }
</style>
