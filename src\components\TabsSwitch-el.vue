<template>
    <div class="tabs-switch">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <!-- <el-tab-pane label="User" name="first">User</el-tab-pane>
            <el-tab-pane label="Config" name="second">Config</el-tab-pane>
            <el-tab-pane label="Role" name="third">Role</el-tab-pane>
            <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            <el-tab-pane v-for="(item, index) in tabs" :key="index" :label="item.name" :name="item.name">
                
            </el-tab-pane>
        </el-tabs>
    </div>
	
</template>
<script setup>
import { ref } from 'vue'
// import { ElTabs, ElTabPane } from 'element-plus'

defineProps({
    tabs: {
        type: Array,
        required: true,
        default: () => []
    }
})
const activeName = ref('first')

const handleClick = (tab, event) => {
  console.log(tab, event)
}
</script>

<style scoped lang="scss">
    .demo-tabs > .el-tabs__content {
        padding: 32px;
        color: #6b778c;
        font-size: 32px;
        font-weight: 600;
    }
    :deep(.el-tabs__nav){
        .el-tabs__item{
            color: #fff;
            font-size: 20px;
            padding: 0 40px;
            height: 42px;
        }
        .is-active{
            color: #2DD8B7;
        }
        .el-tabs__active-bar{
            background-color: #2DD8B7;
            height: 4px;
            border-radius: 8px;
            bottom: 1px;
        }
    }
    :deep(.el-tabs__nav-wrap:after){
        background-color: rgba(8, 139, 255, 0.2);
    }
    :deep(.el-tabs__nav){
        margin-left: 60px;
    }
</style>
