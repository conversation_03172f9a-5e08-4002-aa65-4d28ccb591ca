<template>
  <div class="school-rating">
    
    <!-- 详情内容 -->
    <div class="details-content">
      <!-- 第一行 -->
      <div class="details-row">
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">地址</span>
        </div>
          <div class="detail-value">深圳市南山区科技园文华路2号</div>
        </div>
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">联系方式</span>
        </div>
          <div class="detail-value">
            <span class="phone-number">0755-26632836</span>
            <span class="phone-number">0755-26632839</span>
          </div>
        </div>
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">建校时间</span>
        </div>
          <div class="detail-value">1995年</div>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="details-row">
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">办学规模</span>
        </div>
          <div class="detail-value">6868人</div>
        </div>
        <div class="detail-item">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">足球专项经费年度预算</span>
        </div>
          <div class="detail-value">1000万</div>
        </div>
        <div class="detail-item">
          <!-- 空白占位 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'SchoolRating'
})
</script>

<style src="@/styles/styles.scss"></style>
<style scoped lang="scss">
.school-rating {
  padding: 30px;
  padding-top: 0;
  color: #fff;
  // 详情内容样式
  .details-content {
    .details-row {
      display: flex;
      margin-bottom: 60px;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-item {
        flex: 1;
        margin-right: 60px;

        &:last-child {
          margin-right: 0;
        }

        .detail-label {
          font-size: 14px;
          color: #00D4FF;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .detail-value {
          font-size: 24px;
          color: #ffffff;
          line-height: 1.4;

          .phone-number {
            display: inline-block;
            margin-right: 20px;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .details-content {
      .details-row {
        flex-wrap: wrap;

        .detail-item {
          flex: 0 0 50%;
          margin-bottom: 20px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 20px;

    .details-content {
      .details-row {
        flex-direction: column;

        .detail-item {
          margin-right: 0;
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
