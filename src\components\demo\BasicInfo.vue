<template>
  <div class="basic-info">
    <h3>基础数据</h3>
    <div class="info-grid">
      <div class="info-item">
        <span class="label">学校总数:</span>
        <span class="value">156所</span>
      </div>
      <div class="info-item">
        <span class="label">在校学生:</span>
        <span class="value">45,678人</span>
      </div>
      <div class="info-item">
        <span class="label">教职工:</span>
        <span class="value">3,245人</span>
      </div>
      <div class="info-item">
        <span class="label">足球场地:</span>
        <span class="value">89个</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'BasicInfo'
})
</script>

<style scoped lang="scss">
.basic-info {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  
  h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      
      .label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
      }
      
      .value {
        color: #00d4ff;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }
}
</style>
