<template>
  <div class="conatiner">
    <TitleInfo title="南山梯队" beautiful="NANSHAN TIER" />
    <div class="echelon-tier">
      <ul class="tier-list">
        <li class="tier-item active"><img src="@/assets/images/tier-icon.png" alt="" srcset=""><span>U9</span></li>
        <li class="tier-item"></li>
        <li class="tier-item"></li>
        <li class="tier-item"></li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TitleInfo from '@/components/TitleInfo.vue';

</script>
<style scoped lang="scss">
  .conatiner{
    width: 100%;
    height: 100%;
    .echelon-tier{
      overflow: hidden;
      .tier-list{
        display: flex;
        margin-top: 20px;
        .tier-item{
          display: flex;
          align-items: center;
          box-sizing: border-box;
          width: 375px;
          height: 85px;
          background: url(@/assets/images/u-bg-noselect.png);
          background-size: 100% 100%;
          font-size: 30px;
          color: #fff;
          img{
            width: 55px;
            height: 55px;
            margin-left: 70px;
            margin-right: 20px;
          }
        }
        .active{
            background: url(@/assets/images/u-bg-select.png);
            background-size: 100% 100%;
            color: #13F0C6;
          }
      }
    }
  }
</style>
