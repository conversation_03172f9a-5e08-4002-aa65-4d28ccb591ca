<template>
  <div class="conatiner">
    <TitleInfo title="南山梯队" beautiful="NANSHAN TIER" />
    <div class="echelon-tier">
      <ul class="tier-list">
        <li
          v-for="(tier, index) in tierList"
          :key="tier.id"
          :class="['tier-item', { active: activeTierId === tier.id }]"
          @click="handleTierChange(tier)"
        >
          <img :src="tier.icon" :alt="tier.name">
          <span>{{ tier.name }}</span>
        </li>
      </ul>
    </div>
    <div class="echelon-content">
      <TabsSwitch :tabs="tabs" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, markRaw } from 'vue';
import TitleInfo from '@/components/TitleInfo.vue';
import tierIcon from '@/assets/images/tier-icon.png';
import TabsSwitch from '@/components/TabsSwitch.vue';
import Trainplan from './component/trainplan.vue';
import Teaminfo from './component/teaminfo.vue';
import Tacticalboard from './component/tacticalboard.vue';
import Roster from './component/roster.vue';
import Honor from './component/honor.vue';
import Competitionlog from './component/competitionlog.vue';

//​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '球队信息', component: Teaminfo },
        { name: '球员名单', component: Roster },
        { name: '训练计划', component: Trainplan },
        { name: '比赛日志', component: Competitionlog },
        { name: '战术板', component: Tacticalboard },
        { name: '荣誉殿堂', component: Honor },
    ]))
// 梯队数据
const tierList = ref([
  {
    id: 1,
    name: 'U9',
    icon: tierIcon,
  },
  {
    id: 2,
    name: 'U11',
    icon: tierIcon,
  },
  {
    id: 3,
    name: 'U13',
    icon: tierIcon,
  },
  {
    id: 4,
    name: 'U15',
    icon: tierIcon,
  }
]);

// 当前激活的梯队ID
const activeTierId = ref(1);

// 处理梯队切换
const handleTierChange = (tier) => {
  console.log('切换到梯队:', tier.name);
  activeTierId.value = tier.id;
};
</script>
<style scoped lang="scss">
  .conatiner{
    width: 100%;
    height: 100%;
    .echelon-tier{
      overflow: hidden;
      .tier-list{
        display: flex;
        margin-top: 20px;
        .tier-item{
          display: flex;
          align-items: center;
          box-sizing: border-box;
          width: 375px;
          height: 85px;
          background: url(@/assets/images/u-bg-noselect.png);
          background-size: 100% 100%;
          font-size: 30px;
          color: #fff;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            filter: brightness(1.1);
          }

          img{
            width: 55px;
            height: 55px;
            margin-left: 70px;
            margin-right: 20px;
          }
        }
        .active{
            background: url(@/assets/images/u-bg-select.png);
            background-size: 100% 100%;
            color: #13F0C6;
          }
      }
    }
    .echelon-content{
      width: 1495px;
      height: 575px;
      margin: 0 auto;
      background: rgba(1, 31, 81, 0.7);
      border-radius: 10px;
      margin-top: 10px;
      }
  }
</style>
