<template>
  <div class="teacher-strength">
    <h3>师资力量</h3>
    <div class="teacher-stats">
      <div class="stat-card" v-for="stat in teacherStats" :key="stat.id">
        <div class="stat-icon">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <h4>{{ stat.title }}</h4>
          <p class="stat-number">{{ stat.number }}</p>
          <p class="stat-description">{{ stat.description }}</p>
        </div>
      </div>
    </div>
    
    <div class="teacher-list">
      <h4>优秀教练员</h4>
      <div class="coaches-grid">
        <div class="coach-card" v-for="coach in coaches" :key="coach.id">
          <div class="coach-avatar">
            <img :src="coach.avatar" :alt="coach.name">
          </div>
          <div class="coach-info">
            <h5>{{ coach.name }}</h5>
            <p class="coach-title">{{ coach.title }}</p>
            <p class="coach-experience">{{ coach.experience }}</p>
            <div class="coach-achievements">
              <span v-for="achievement in coach.achievements" :key="achievement" class="achievement">
                {{ achievement }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'TeacherStrength'
})

const teacherStats = ref([
  {
    id: 1,
    title: '专业教练',
    number: '245人',
    description: '持证专业足球教练',
    icon: 'fas fa-user-tie'
  },
  {
    id: 2,
    title: '国际认证',
    number: '89人',
    description: 'FIFA/AFC认证教练',
    icon: 'fas fa-certificate'
  },
  {
    id: 3,
    title: '退役球员',
    number: '56人',
    description: '职业球员转型教练',
    icon: 'fas fa-medal'
  },
  {
    id: 4,
    title: '培训时长',
    number: '1,200小时',
    description: '年度专业培训',
    icon: 'fas fa-clock'
  }
])

const coaches = ref([
  {
    id: 1,
    name: '张教练',
    title: '主教练',
    experience: '15年执教经验',
    avatar: '/api/placeholder/60/60',
    achievements: ['亚足联A级', '青训专家']
  },
  {
    id: 2,
    name: '李教练',
    title: '技术总监',
    experience: '12年执教经验',
    avatar: '/api/placeholder/60/60',
    achievements: ['FIFA认证', '战术分析师']
  },
  {
    id: 3,
    name: '王教练',
    title: '体能教练',
    experience: '8年执教经验',
    avatar: '/api/placeholder/60/60',
    achievements: ['运动康复师', '营养师']
  }
])
</script>

<style scoped lang="scss">
.teacher-strength {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  
  h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .teacher-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 30px;
    
    .stat-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      
      .stat-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        border-radius: 8px;
        
        i {
          font-size: 18px;
          color: #fff;
        }
      }
      
      .stat-content {
        h4 {
          color: #fff;
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .stat-number {
          color: #00d4ff;
          font-weight: 700;
          font-size: 16px;
          margin-bottom: 2px;
        }
        
        .stat-description {
          color: rgba(255, 255, 255, 0.7);
          font-size: 11px;
        }
      }
    }
  }
  
  .teacher-list {
    h4 {
      color: #fff;
      margin-bottom: 16px;
      font-size: 16px;
    }
    
    .coaches-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
      
      .coach-card {
        display: flex;
        gap: 16px;
        padding: 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-2px);
        }
        
        .coach-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          overflow: hidden;
          background: linear-gradient(135deg, #00d4ff, #0099cc);
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .coach-info {
          flex: 1;
          
          h5 {
            color: #fff;
            font-size: 16px;
            margin-bottom: 4px;
          }
          
          .coach-title {
            color: #00d4ff;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
          }
          
          .coach-experience {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-bottom: 8px;
          }
          
          .coach-achievements {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            
            .achievement {
              padding: 2px 8px;
              background: rgba(0, 212, 255, 0.2);
              color: #00d4ff;
              border-radius: 12px;
              font-size: 10px;
              border: 1px solid rgba(0, 212, 255, 0.3);
            }
          }
        }
      }
    }
  }
}
</style>
