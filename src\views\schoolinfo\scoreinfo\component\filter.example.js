// 筛选方法使用示例

// 1. 基础位置筛选（已有功能改进）
function exampleBasicFilter() {
  // 筛选前锋
  handleKeyChange('前锋');
  
  // 显示全部
  handleKeyChange('全部');
}

// 2. 多条件筛选示例
function exampleMultiFilter() {
  // 筛选年龄在20-25岁之间的前锋
  filterData({
    position: '前锋',
    minAge: 20,
    maxAge: 25
  });
  
  // 筛选有AI档案的中场球员
  filterData({
    position: '中场',
    aiArchives: true
  });
  
  // 按姓名搜索
  filterData({
    name: '张'
  });
  
  // 复合条件：年龄大于25岁且有AI档案的球员
  filterData({
    minAge: 25,
    aiArchives: true
  });
}

// 3. 在组件中的实际使用方法
export function usePlayerFilter() {
  // 位置筛选选项
  const positionOptions = ['全部', '前锋', '中场', '后卫', '守门员'];
  
  // AI档案筛选选项
  const archiveOptions = [
    { label: '全部', value: undefined },
    { label: '有档案', value: true },
    { label: '无档案', value: false }
  ];
  
  // 年龄范围筛选
  const ageRanges = [
    { label: '全部', min: undefined, max: undefined },
    { label: '20岁以下', min: undefined, max: 19 },
    { label: '20-25岁', min: 20, max: 25 },
    { label: '25岁以上', min: 26, max: undefined }
  ];
  
  // 筛选状态
  const filterState = {
    position: '全部',
    aiArchives: undefined,
    ageRange: { min: undefined, max: undefined },
    searchName: ''
  };
  
  // 应用筛选
  const applyFilter = () => {
    const filters = {
      position: filterState.position,
      aiArchives: filterState.aiArchives,
      minAge: filterState.ageRange.min,
      maxAge: filterState.ageRange.max,
      name: filterState.searchName
    };
    
    filterData(filters);
  };
  
  // 重置所有筛选
  const resetAllFilters = () => {
    filterState.position = '全部';
    filterState.aiArchives = undefined;
    filterState.ageRange = { min: undefined, max: undefined };
    filterState.searchName = '';
    resetFilter();
  };
  
  return {
    positionOptions,
    archiveOptions,
    ageRanges,
    filterState,
    applyFilter,
    resetAllFilters
  };
}

// 4. 高级筛选示例
function exampleAdvancedFilter() {
  // 筛选进球数大于5的前锋
  const topScorers = originalData.value.filter(player => 
    player.position === '前锋' && player.goals > 5
  );
  
  // 筛选助攻数大于进球数的中场
  const playmakers = originalData.value.filter(player => 
    player.position === '中场' && player.assists > player.goals
  );
  
  // 筛选出场次数最多的球员
  const mostAppearances = originalData.value.reduce((max, player) => 
    player.appearances > max.appearances ? player : max
  );
  
  // 按AI评分排序
  const sortedByScore = [...originalData.value].sort((a, b) => b.aiScore - a.aiScore);
  
  console.log('顶级射手:', topScorers);
  console.log('组织核心:', playmakers);
  console.log('出场王:', mostAppearances);
  console.log('评分排行:', sortedByScore);
}

// 5. 实时搜索示例
function setupRealTimeSearch() {
  let searchTimeout = null;
  
  const handleSearch = (searchTerm) => {
    // 防抖处理
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      filterData({ name: searchTerm });
    }, 300);
  };
  
  return { handleSearch };
}

// 6. 筛选历史记录
class FilterHistory {
  constructor() {
    this.history = [];
    this.currentIndex = -1;
  }
  
  // 添加筛选记录
  addFilter(filters) {
    this.history = this.history.slice(0, this.currentIndex + 1);
    this.history.push(filters);
    this.currentIndex = this.history.length - 1;
  }
  
  // 撤销筛选
  undo() {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      const filters = this.history[this.currentIndex];
      filterData(filters);
      return filters;
    }
    return null;
  }
  
  // 重做筛选
  redo() {
    if (this.currentIndex < this.history.length - 1) {
      this.currentIndex++;
      const filters = this.history[this.currentIndex];
      filterData(filters);
      return filters;
    }
    return null;
  }
  
  // 清空历史
  clear() {
    this.history = [];
    this.currentIndex = -1;
  }
}

export { FilterHistory };
