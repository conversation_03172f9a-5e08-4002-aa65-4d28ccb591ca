// // 1. 监听整个路由对象
// watch(route, (newRoute, oldRoute) => {
//     console.log('🔄 路由变化检测')
//     console.log('从:', oldRoute ? .fullPath)
//     console.log('到:', newRoute.fullPath)

//     // 执行路由变化后的逻辑
//     onRouteChange(newRoute, oldRoute)
// }, {
//     deep: true,
//     immediate: true
// })

// // 2. 只监听路径变化
// watch(() => route.path, (newPath, oldPath) => {
//     if (newPath !== oldPath) {
//         console.log('📍 路径变化:', oldPath, '->', newPath)
//         onPathChange(newPath, oldPath)
//     }
// })

// // 3. 监听路由名称变化
// watch(() => route.name, (newName, oldName) => {
//     if (newName !== oldName) {
//         console.log('🏷️ 页面切换:', oldName, '->', newName)
//         onPageChange(newName, oldName)
//     }
// })

// // 4. 监听路由参数变化
// watch(() => route.params, (newParams, oldParams) => {
//     console.log('🔧 参数变化:', oldParams, '->', newParams)
//     onParamsChange(newParams, oldParams)
// }, {
//     deep: true
// })

// 5. 监听查询参数变化
// watch(() => route.query, (newQuery, oldQuery) => {
//     console.log('🔍 查询变化:', oldQuery, '->', newQuery)
//     onQueryChange(newQuery, oldQuery)
// }, {
//     deep: true
// })

// const onPathChange = (newPath, oldPath) => {
//     // 路径变化时的特定逻辑
//     console.log('处理路径变化逻辑')
// }

import { watch } from 'vue';
import { useRoute } from 'vue-router';

// 监听路由变化的工具函数
export function watchRoute(callback, options = {}) {
    const route = useRoute();

    // 默认选项
    const defaultOptions = {
        immediate: false, // 默认不立即执行，避免初始化时的重复调用
        deep: true // 深度监听对象变化
    };

    const finalOptions = {...defaultOptions, ...options };

    // 监听查询参数变化
    const stopWatcher = watch(() => route.query, (newQuery, oldQuery) => {
        // 如果提供了回调函数，执行回调
        if (callback && typeof callback === 'function') {
            callback(newQuery, oldQuery);
        }

        // 如果有key参数，返回key值
        if (newQuery.key) {
            return newQuery;
        }
    }, finalOptions);

    // 返回停止监听的函数
    return stopWatcher;
}

// 监听路由路径变化
export function watchRoutePath(callback) {
    const route = useRoute();

    const stopWatcher = watch(() => route.path, (newPath, oldPath) => {
        if (callback && typeof callback === 'function') {
            callback(newPath, oldPath);
        }
    }, { immediate: true });

    return stopWatcher;
}

// 监听路由参数变化
export function watchRouteParams(callback) {
    const route = useRoute();

    const stopWatcher = watch(() => route.params, (newParams, oldParams) => {
        if (callback && typeof callback === 'function') {
            callback(newParams, oldParams);
        }
    }, {
        immediate: true,
        deep: true
    });

    return stopWatcher;
}

// 监听完整路由变化
export function watchFullRoute(callback) {
    const route = useRoute();

    const stopWatcher = watch(route, (newRoute, oldRoute) => {
        if (callback && typeof callback === 'function') {
            callback(newRoute, oldRoute);
        }
    }, {
        immediate: true,
        deep: true
    });

    return stopWatcher;
}