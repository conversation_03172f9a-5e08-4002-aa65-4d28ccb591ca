// // 1. 监听整个路由对象
// watch(route, (newRoute, oldRoute) => {
//     console.log('🔄 路由变化检测')
//     console.log('从:', oldRoute ? .fullPath)
//     console.log('到:', newRoute.fullPath)

//     // 执行路由变化后的逻辑
//     onRouteChange(newRoute, oldRoute)
// }, {
//     deep: true,
//     immediate: true
// })

// // 2. 只监听路径变化
// watch(() => route.path, (newPath, oldPath) => {
//     if (newPath !== oldPath) {
//         console.log('📍 路径变化:', oldPath, '->', newPath)
//         onPathChange(newPath, oldPath)
//     }
// })

// // 3. 监听路由名称变化
// watch(() => route.name, (newName, oldName) => {
//     if (newName !== oldName) {
//         console.log('🏷️ 页面切换:', oldName, '->', newName)
//         onPageChange(newName, oldName)
//     }
// })

// // 4. 监听路由参数变化
// watch(() => route.params, (newParams, oldParams) => {
//     console.log('🔧 参数变化:', oldParams, '->', newParams)
//     onParamsChange(newParams, oldParams)
// }, {
//     deep: true
// })

// 5. 监听查询参数变化
// watch(() => route.query, (newQuery, oldQuery) => {
//     console.log('🔍 查询变化:', oldQuery, '->', newQuery)
//     onQueryChange(newQuery, oldQuery)
// }, {
//     deep: true
// })

// const onPathChange = (newPath, oldPath) => {
//     // 路径变化时的特定逻辑
//     console.log('处理路径变化逻辑')
// }


export function watchRoute() {
    watch(() => route.query, (newQuery) => {
        if (newQuery.key) {
            return newQuery.key;
        }
    }, { immediate: true }); // 首次加载也执行
}