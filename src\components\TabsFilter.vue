<template>
  <div class="tabs-filter">
    <div class="tabs-header">
      <div class="tabs-nav">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab-item', { 'active': activeTab === tab.name }]"
          @click="handleTabClick(tab, index)"
        >
          {{ tab.title }}
        </div>
      </div>
      <div class="underline" :style="underlineStyle"></div>
    </div>

    <div class="tabs-content">
      <component
        :is="fixedComponent"
        v-if="fixedComponent"
        :filter-data="currentFilterData"
        v-bind="componentProps"
      />
      <div v-else class="empty-content">
        请配置固定组件
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  // tabs配置数组，每个tab包含 { title: '标题', name: '唯一标识', data: '筛选数据' }
  tabs: {
    type: Array,
    required: true,
    default: () => []
  },
  // 固定的组件，用于展示筛选结果
  fixedComponent: {
    type: [String, Object],
    required: true
  },
  // 默认激活的标签
  defaultActive: {
    type: [String, Number],
    default: 0
  },
  // 传递给固定组件的额外props
  componentProps: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['tab-change'])

const activeTab = ref('')
const activeIndex = ref(0)

// 当前筛选数据
const currentFilterData = computed(() => {
  const currentTab = props.tabs.find(tab => tab.name === activeTab.value)
  return currentTab ? currentTab.data : null
})

// 下划线样式
const underlineStyle = computed(() => {
  const tabWidth = 100 / props.tabs.length
  return {
    width: `${tabWidth}%`,
    transform: `translateX(${activeIndex.value * 100}%)`
  }
})

// 初始化激活的标签
const initActiveTab = () => {
  if (props.tabs && props.tabs.length > 0) {
    if (typeof props.defaultActive === 'number') {
      // 如果是数字索引
      const index = Math.min(props.defaultActive, props.tabs.length - 1)
      activeIndex.value = index
      activeTab.value = props.tabs[index].name
    } else {
      // 如果是字符串名称
      const foundIndex = props.tabs.findIndex(tab => tab.name === props.defaultActive)
      if (foundIndex !== -1) {
        activeIndex.value = foundIndex
        activeTab.value = props.defaultActive
      } else {
        activeIndex.value = 0
        activeTab.value = props.tabs[0].name
      }
    }
  }
}

// 标签点击处理
const handleTabClick = (tab, index) => {
  activeTab.value = tab.name
  activeIndex.value = index

  // 触发事件
  emit('tab-change', {
    tab: tab,
    index: index,
    data: tab.data
  })
}

// 初始化
onMounted(() => {
  initActiveTab()
})
</script>

<style scoped lang="scss">
.tabs-filter {
  width: 100%;

  .tabs-header {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 20px;

    .tabs-nav {
      display: flex;
      position: relative;
      z-index: 2;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 6px;

        &:hover {
          color: rgba(255, 255, 255, 0.9);
        }

        &.active {
          color: #ffffff;
          font-weight: 600;
        }
      }
    }

    .underline {
      position: absolute;
      bottom: 4px;
      left: 4px;
      height: calc(100% - 8px);
      background: linear-gradient(135deg, #2DD8B7, #1BC5A8);
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
      box-shadow: 0 2px 8px rgba(45, 216, 183, 0.3);
    }
  }

  .tabs-content {
    .empty-content {
      padding: 40px 20px;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      font-size: 16px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px dashed rgba(255, 255, 255, 0.2);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .tabs-header {
      .tabs-nav {
        .tab-item {
          padding: 10px 12px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>

