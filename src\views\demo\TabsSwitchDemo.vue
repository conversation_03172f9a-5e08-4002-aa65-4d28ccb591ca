<template>
  <div class="tabs-demo">
    <div class="demo-header">
      <h2>TabsSwitch 切换插件演示</h2>
      <p>支持动态组件切换，带有流畅的下划线过渡效果</p>
    </div>
    
    <!-- 基础用法 -->
    <div class="demo-section">
      <h3>基础用法</h3>
      <TabsSwitch 
        :tabs="basicTabs"
        @change="onTabChange"
        @tab-click="onTabClick"
      />
    </div>
    
    <!-- 不同动画效果 -->
    <div class="demo-section">
      <h3>滑动动画</h3>
      <TabsSwitch 
        :tabs="basicTabs"
        animation-type="slide"
        :default-active="1"
      />
    </div>
    
    <div class="demo-section">
      <h3>淡入淡出动画</h3>
      <TabsSwitch 
        :tabs="basicTabs"
        animation-type="fade"
        :default-active="2"
      />
    </div>
    
    <div class="demo-section">
      <h3>缩放动画</h3>
      <TabsSwitch 
        :tabs="basicTabs"
        animation-type="scale"
        :default-active="3"
      />
    </div>
    
    <!-- 传递props给组件 -->
    <div class="demo-section">
      <h3>传递Props给组件</h3>
      <TabsSwitch 
        :tabs="propsTabsExample"
        :component-props="componentProps"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TabsSwitch from '@/components/TabsSwitch.vue'
import BasicInfo from '@/components/demo/BasicInfo.vue'
import SchoolRating from '@/components/demo/SchoolRating.vue'
import HardwareFacilities from '@/components/demo/HardwareFacilities.vue'
import TeacherStrength from '@/components/demo/TeacherStrength.vue'

// 基础标签配置
const basicTabs = ref([
  { name: '基础数据', component: BasicInfo },
  { name: '学校评情', component: SchoolRating },
  { name: '硬件设施', component: HardwareFacilities },
  { name: '师资力量', component: TeacherStrength }
])

// 带props的标签示例
const propsTabsExample = ref([
  { name: '基础数据', component: BasicInfo },
  { name: '学校评情', component: SchoolRating }
])

// 传递给组件的props
const componentProps = ref({
  customData: '这是传递给组件的数据',
  theme: 'dark'
})

// 事件处理
const onTabChange = (data) => {
  console.log('标签切换:', data)
}

const onTabClick = (data) => {
  console.log('标签点击:', data)
}
</script>

<style scoped lang="scss">
.tabs-demo {
  padding: 40px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  
  .demo-header {
    text-align: center;
    margin-bottom: 40px;
    
    h2 {
      color: #fff;
      font-size: 32px;
      margin-bottom: 12px;
      font-weight: 700;
    }
    
    p {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
    }
  }
  
  .demo-section {
    margin-bottom: 40px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    h3 {
      color: #fff;
      font-size: 20px;
      margin-bottom: 20px;
      font-weight: 600;
    }
  }
}
</style>
