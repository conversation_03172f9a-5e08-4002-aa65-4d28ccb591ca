//搭配的样式穿透，整个表格都透明了
:deep(.el-table__expanded-cell),
:deep(.el-table),
:deep(.el-table th),
:deep(.el-table tr),
:deep(.el-table td) {
    background-color: transparent;
}

//去掉了内容的全部边框
:deep(.el-table__row>td) {
    border: none;
}

//去掉了head的boder-bottom
:deep(.el-table th.el-table__cell.is-leaf) {
    border: none;
}

//修改了hover的背景色
:deep(.el-table__body tr:hover > td) {
    background-color: rgba(200, 200, 200, 0.2) !important;
}

//去掉最底下，那个最难找的线，白色线
:deep(.el-table__inner-wrapper::before) {
    height: 0;
}

:deep(.el-table .warning-row) {
    background-color: rgba(8, 139, 255, 0.1);
}

.aiArchives {
    height: 21px;
    line-height: 21px;
    border: 1px solid #13F0C6;
    // padding: 0px 8px;
    border-radius: 13px;
    font-size: 12px;
    color: #13F0C6;
    cursor: pointer;
    text-align: center;
    width: 90px;
}