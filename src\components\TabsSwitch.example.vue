<template>
  <div class="example-container">
    <h2>TabsSwitch 使用示例</h2>
    
    <!-- 示例1: 普通字符串name -->
    <h3>示例1: 普通字符串name</h3>
    <TabsSwitch :tabs="normalTabs" :default-active="0" />
    
    <!-- 示例2: 字符串数组name -->
    <h3>示例2: 字符串数组name（共享组件）</h3>
    <TabsSwitch :tabs="arrayTabs" :default-active="0" />
    
    <!-- 示例3: 混合使用 -->
    <h3>示例3: 混合使用</h3>
    <TabsSwitch :tabs="mixedTabs" :default-active="0" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TabsSwitch from './TabsSwitch.vue'

// 示例组件
const ExampleComponent1 = {
  template: '<div style="padding: 20px; background: rgba(45, 216, 183, 0.1); border-radius: 8px; color: #fff;">这是示例组件1的内容</div>'
}

const ExampleComponent2 = {
  template: '<div style="padding: 20px; background: rgba(255, 193, 7, 0.1); border-radius: 8px; color: #fff;">这是示例组件2的内容</div>'
}

const SharedComponent = {
  template: '<div style="padding: 20px; background: rgba(220, 53, 69, 0.1); border-radius: 8px; color: #fff;">这是共享组件的内容 - 多个tab共享同一个组件</div>'
}

// 示例1: 普通字符串name
const normalTabs = ref([
  {
    name: '用户管理',
    component: ExampleComponent1
  },
  {
    name: '系统设置',
    component: ExampleComponent2
  },
  {
    name: '权限管理'
    // 没有component，会显示默认内容
  }
])

// 示例2: 字符串数组name（多个tab共享一个组件）
const arrayTabs = ref([
  {
    name: ['数据统计', '用户统计', '销售统计'], // 数组形式的name
    component: SharedComponent // 这三个tab都会使用同一个组件
  },
  {
    name: '其他功能',
    component: ExampleComponent1
  }
])

// 示例3: 混合使用
const mixedTabs = ref([
  {
    name: '首页',
    component: ExampleComponent1
  },
  {
    name: ['报表中心', '数据分析', '图表展示'], // 数组name
    component: SharedComponent // 共享组件
  },
  {
    name: '设置',
    component: ExampleComponent2
  },
  {
    name: ['帮助', '关于'], // 另一个数组name
    component: ExampleComponent1 // 共享另一个组件
  }
])
</script>

<style scoped>
.example-container {
  padding: 20px;
  color: #fff;
}

h2, h3 {
  color: #2DD8B7;
  margin: 20px 0 10px 0;
}

h2 {
  border-bottom: 2px solid #2DD8B7;
  padding-bottom: 10px;
}
</style>
