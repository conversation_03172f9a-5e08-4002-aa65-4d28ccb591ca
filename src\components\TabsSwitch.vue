<template>
  <div class="tabs-switch">
    <!-- 标签头部 -->
    <div class="tabs-header">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: activeIndex === index }"
        @click="switchTab(index)"
        ref="tabRefs"
      >
        {{ tab.name }}
      </div>
      <!-- 下划线指示器 -->
      <div
        class="underline"
        :style="underlineStyle"
      ></div>
    </div>

    <!-- 内容区域 -->
    <div class="tabs-content">
      <transition
        :name="transitionName"
        mode="out-in"
        @before-enter="onBeforeEnter"
        @enter="onEnter"
        @leave="onLeave"
      >
        <component
          :is="currentComponent"
          :key="activeIndex"
          v-bind="componentProps"
        />
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'

defineOptions({
  name: 'TabsSwitch'
})

// Props定义
const props = defineProps({
  // 标签数据 [{ name: '标签名', component: '组件名' }]
  tabs: {
    type: Array,
    required: true,
    default: () => []
  },
  // 默认激活的标签索引
  defaultActive: {
    type: Number,
    default: 0
  },
  // 传递给组件的props
  componentProps: {
    type: Object,
    default: () => ({})
  },
  // 切换动画类型
  animationType: {
    type: String,
    default: 'slide', // slide, fade, scale
    validator: (value) => ['slide', 'fade', 'scale'].includes(value)
  }
})

// Emits定义
const emit = defineEmits(['change', 'tab-click'])

// 响应式数据
const activeIndex = ref(props.defaultActive)
const tabRefs = ref([])
const underlineStyle = ref({})
const transitionName = ref('tab-slide')

// 计算属性
const currentComponent = computed(() => {
  if (props.tabs[activeIndex.value]) {
    return props.tabs[activeIndex.value].component
  }
  return null
})

// 切换标签
const switchTab = (index) => {
  if (index === activeIndex.value) return

  const oldIndex = activeIndex.value
  activeIndex.value = index

  // 更新过渡动画方向
  updateTransitionName(oldIndex, index)

  // 更新下划线位置
  updateUnderline()

  // 触发事件
  emit('change', {
    index,
    tab: props.tabs[index],
    oldIndex
  })

  emit('tab-click', {
    index,
    tab: props.tabs[index]
  })
}

// 更新过渡动画名称
const updateTransitionName = (oldIndex, newIndex) => {
  const direction = newIndex > oldIndex ? 'next' : 'prev'
  transitionName.value = `tab-${props.animationType}-${direction}`
}

// 更新下划线位置和宽度
const updateUnderline = async () => {
  await nextTick()

  if (tabRefs.value && tabRefs.value[activeIndex.value]) {
    const activeTab = tabRefs.value[activeIndex.value]
    const { offsetLeft, offsetWidth } = activeTab

    underlineStyle.value = {
      transform: `translateX(${offsetLeft}px)`,
      width: `${offsetWidth}px`
    }
  }
}

// 动画钩子
const onBeforeEnter = (el) => {
  el.style.opacity = '0'
}

const onEnter = (el) => {
  el.offsetHeight // 触发重排
  el.style.opacity = '1'
}

const onLeave = (el) => {
  el.style.opacity = '0'
}

// 监听tabs变化，重新计算下划线
watch(() => props.tabs, () => {
  nextTick(() => {
    updateUnderline()
  })
}, { deep: true })

// 监听activeIndex变化
watch(activeIndex, () => {
  updateUnderline()
})

// 组件挂载后初始化下划线位置
onMounted(() => {
  nextTick(() => {
    updateUnderline()
  })
})

// 暴露方法给父组件
defineExpose({
  switchTab,
  activeIndex: computed(() => activeIndex.value),
  currentTab: computed(() => props.tabs[activeIndex.value])
})
</script>

<style scoped lang="scss">
.tabs-switch {
  width: 100%;

  .tabs-header {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 4px;
    backdrop-filter: blur(10px);

    .tab-item {
      flex: 1;
      padding: 12px 20px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.7);
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      z-index: 2;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      &.active {
        color: #fff;
        font-weight: 600;
      }
    }

    .underline {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px;
      background: linear-gradient(90deg, #00d4ff, #0099cc);
      border-radius: 2px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
      box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
    }
  }

  .tabs-content {
    margin-top: 20px;
    position: relative;
    overflow: hidden;
  }
}

// 滑动动画
.tab-slide-next-enter-active,
.tab-slide-prev-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-slide-next-leave-active,
.tab-slide-prev-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-slide-next-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.tab-slide-next-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.tab-slide-prev-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.tab-slide-prev-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 淡入淡出动画
.tab-fade-next-enter-active,
.tab-fade-prev-enter-active,
.tab-fade-next-leave-active,
.tab-fade-prev-leave-active {
  transition: all 0.3s ease-in-out;
}

.tab-fade-next-enter-from,
.tab-fade-prev-enter-from,
.tab-fade-next-leave-to,
.tab-fade-prev-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

// 缩放动画
.tab-scale-next-enter-active,
.tab-scale-prev-enter-active,
.tab-scale-next-leave-active,
.tab-scale-prev-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-scale-next-enter-from,
.tab-scale-prev-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(10px);
}

.tab-scale-next-leave-to,
.tab-scale-prev-leave-to {
  opacity: 0;
  transform: scale(1.1) translateY(-10px);
}
</style>
