<template>
    <div class="tabs-switch ">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" >
            <!-- <el-tab-pane label="User" name="first">User</el-tab-pane>
            <el-tab-pane label="Config" name="second">Config</el-tab-pane>
            <el-tab-pane label="Role" name="third">Role</el-tab-pane>
            <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            <el-tab-pane v-for="(item, index) in processedTabs" :key="index" :label="getTabLabel(item)" :name="getTabName(item)">
              <template v-if="tabs.length>1">
                <component
                    :is="getTabComponent(item)"
                    v-if="getTabComponent(item)"
                    v-bind="componentProps"
                />
              </template>
              
                <!-- <div v-else class="empty-content">
                    {{ getTabLabel(item) }} 内容
                </div> -->
                
            </el-tab-pane>
        </el-tabs>
        <component v-if="tabs.length===1" :is="tabs[0].component"></component>
    </div>
	
</template>
<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
// import { ElTabs, ElTabPane } from 'element-plus'

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
        default: () => []
    },
    // 默认激活的标签
    defaultActive: {
        type: [String, Number],
        default: 0
    },
    // 传递给组件的props
    componentProps: {
        type: Object,
        default: () => ({})
    }
})

// 定义事件
// const emit = defineEmits(['change', 'tab-click'])

const activeName = ref('');

// 处理tabs数据，将name为数组的情况展开为多个tab
const processedTabs = computed(() => {
    const result = [];

    props.tabs.forEach((tab, index) => {
        if (Array.isArray(tab.name)) {
            // 如果name是数组，为每个name创建一个tab，但共享同一个component
            tab.name.forEach((name, nameIndex) => {
                result.push({
                    ...tab,
                    name: name,
                    originalIndex: index,
                    nameIndex: nameIndex,
                    isArrayItem: true
                });
            });
        } else {
            // 如果name是字符串，直接添加
            result.push({
                ...tab,
                originalIndex: index,
                isArrayItem: false
            });
        }
    });

    return result;
});

// 获取tab标签显示名称
const getTabLabel = (item) => {
    return item.name;
};

// 获取tab的name值（用于el-tab-pane的name属性）
const getTabName = (item) => {
    return item.name;
};

// 获取tab对应的组件
const getTabComponent = (item) => {
    if (item.isArrayItem) {
        // 如果是数组项，使用原始tab的component
        return item.component;
    }
    return item.component;
};

// 初始化激活的标签
const initActiveTab = () => {
    if (processedTabs.value && processedTabs.value.length > 0) {
        if (typeof props.defaultActive === 'number') {
            // 如果是数字索引
            const index = Math.min(props.defaultActive, processedTabs.value.length - 1);
            activeName.value = processedTabs.value[index].name;
        } else {
            // 如果是字符串名称
            const found = processedTabs.value.find(tab => tab.name === props.defaultActive);
            activeName.value = found ? found.name : processedTabs.value[0].name;
        }
    }
};

// 初始化
initActiveTab();

// 标签点击处理
const handleClick = (tab) => {
    const tabName = tab.props.name;
    const tabData = processedTabs.value.find(t => t.name === tabName);

    if (tabData) {
        console.log('切换到标签:', tabData.name);
        console.log('标签数据:', tabData);
        console.log('当前路径:', route.path);
        if(!tabData.iskey){ // 是否对点击标签，加入路由参数
            return;
        }
        router.push({
            path: route.path,
            query: { key: tabData.name } // 数据会显示在URL中
        });
    }
}
</script>

<style scoped lang="scss">

   .tabs-switch :deep(.el-tabs__header){
        margin-top: 8px;
    }
    .tabs-switch .demo-tabs > .el-tabs__content {
        padding: 32px;
        color: #6b778c;
        font-size: 32px;
        font-weight: 600;
    }
    .tabs-switch :deep(.el-tabs__nav){
        .el-tabs__item{
            color: #fff;
            font-size: 20px;
            padding: 0 40px;
            height: 42px;
        }
        .is-active{
            color: #2DD8B7;
        }
        .el-tabs__active-bar{
            background-color: #2DD8B7;
            height: 4px;
            border-radius: 8px;
            bottom: 1px;
        }
    }
    .tabs-switch :deep(.el-tabs__nav-wrap:after){
        background-color: rgba(8, 139, 255, 0.2);
    }
    .tabs-switch :deep(.el-tabs__nav){
        margin-left: 60px;
    }

    .tabs-switch .empty-content {
        padding: 40px 20px;
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin: 20px 0;
    }
</style>
