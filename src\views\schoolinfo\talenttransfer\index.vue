<template>
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    import { ref, markRaw } from 'vue';
    import TabsSwitch from '@/components/TabsSwitch-el.vue';
    import talenttransferrecord from '@/views/schoolinfo/talenttransfer/component/talenttransferrecord.vue'; // 导入组件
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '输送记录', component: talenttransferrecord },
        { name: '升学通道', component:talenttransferrecord },
        
    ]))
</script>


<style scoped>

</style>
