<template>
    <div class="school-title">
        <span class="title-word">{{title}}</span>
        <span class="title-beautiful">{{beautiful}}</span>
    </div>
</template>
<script setup>
let props = defineProps({
    title: {
        type: String,
        default: ''
    },
    beautiful: {
        type: String,
        default: ''
    }
})
</script>
<style lang="scss" scoped>
    .school-title{
            margin-bottom: 21px;
            .title-word{
                margin-left: 7px;
                font-family: YouSheBiaoTiHei;
                font-size: 33px;
                -webkit-text-fill-color: transparent;
                background: -webkit-linear-gradient(top, rgb(255, 255, 255), #90DEFF) text;
            }
            .title-beautiful{
                margin-left: 19px;
                font-size: 15px;
                color: #5484A0;
            }
        }
</style>