<template>
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    import { ref, markRaw, onMounted } from 'vue';
    import TabsSwitch from '@/components/TabsSwitch.vue';
    import scoreinfofilter from '@/views/schoolinfo/scoreinfo/component/scoreinfofilter.vue'; // 导入组件
    //​​使用 markRaw 显式标记组件​,不然会出警告
    // const tabs = ref(markRaw([
    //     { name: '全部', component: scoreinfofilter ,keywords:'全部'},
    //     { name: '前锋', component: scoreinfofilter ,keywords:'前锋'},
    //     { name: '中场', component: scoreinfofilter ,keywords:'中场' },
    //     { name: '后卫', component: scoreinfofilter ,keywords:'后卫' },
    //     { name: '守门员', component: scoreinfofilter ,keywords:'守门员' }
    // ]))
    const tabs=ref(markRaw([
        { name: ['全部','前锋', '中场', '后卫', '守门员'], component: scoreinfofilter }, // 位置信息tab 
    ]))
    
</script>


<style scoped>

</style>