<template>
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    import { ref, markRaw } from 'vue';
    import TabsSwitch from '@/components/TabsSwitch-el.vue';
    import scoreinfofilter from '@/views/schoolinfo/scoreinfo/component/scoreinfofilter.vue'; // 导入组件
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '全部', component: scoreinfofilter },
        { name: '前锋', component: scoreinfofilter },
        { name: '中场', component: scoreinfofilter },
        { name: '后卫', component: scoreinfofilter },
        { name: '守门员', component: scoreinfofilter }
    ]))
</script>


<style scoped>

</style>