<template>
    <TabsSwitch :tabs="tabs" />
</template>
<script setup>
    import { ref, markRaw, onMounted } from 'vue';
    import TabsSwitch from '@/components/TabsSwitch-el.vue';
    import scoreinfofilter from '@/views/schoolinfo/scoreinfo/component/scoreinfofilter.vue'; // 导入组件
    import { watchRoute } from '@/utils/watchRoute';
    //​​使用 markRaw 显式标记组件​,不然会出警告
    const tabs = ref(markRaw([
        { name: '全部', component: scoreinfofilter ,keywords:'全部'},
        { name: '前锋', component: scoreinfofilter ,keywords:'前锋'},
        { name: '中场', component: scoreinfofilter ,keywords:'中场' },
        { name: '后卫', component: scoreinfofilter ,keywords:'后卫' },
        { name: '守门员', component: scoreinfofilter ,keywords:'守门员' }
    ]))
    onMounted(() => {
        watchRoute((newQuery, oldQuery) => {
        console.log('查询参数变化:', oldQuery, '->', newQuery);
        // 在这里添加您的业务逻辑
        // 例如：重新获取数据、更新筛选条件等
    }, {
        immediate: true // 如果需要立即执行，可以传入这个选项
    });
    });
    
</script>


<style scoped>

</style>