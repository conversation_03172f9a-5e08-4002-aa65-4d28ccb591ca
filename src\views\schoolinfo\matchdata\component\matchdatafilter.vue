<template>
  <div class="match-data-filter">
    <ul class="match-list">
      <!-- <li
        v-for="(matchPair, index) in matchPairs"
        :key="index"
        class="match-row"
      >
        <div
          v-for="(match, matchIndex) in matchPair"
          :key="matchIndex"
          class="match-item"
        >
          <div class="match-info">
            <div class="match-header">
              <div class="match-type">{{ match.matchType }}</div>
              <div class="match-datetime">{{ match.date }} {{ match.time }}</div>
            </div>
            <div class="match-teams">
              <div class="team home-team">
                <img :src="match.homeTeam.logo" :alt="match.homeTeam.name" class="team-logo">
                <span class="team-name">{{ match.homeTeam.name }}</span>
              </div>
              <div class="match-score">{{ match.homeScore }}</div>
              <div class="team away-team">
                <img :src="match.awayTeam.logo" :alt="match.awayTeam.name" class="team-logo">
                <span class="team-name">{{ match.awayTeam.name }}</span>
              </div>
              <div class="match-score">{{ match.awayScore }}</div>
            </div>
            <div class="match-actions">
              <div :class="['result-badge', getResultClass(match)]">
                {{ getResultText(match) }}
              </div>
              <button class="detail-btn">详情</button>
            </div>
          </div>
        </div>
      </li> -->
    
      <li class="match-item" v-for="item in matchData">
            <div class="match-info">
                <p>{{item.matchType}}</p>
                <p>{{item.date}} {{item.time}}</p>
            </div>
            <div class="match-shcool">
                <div class="school"><img :src="item.homeTeam.logo" alt="">{{item.homeTeam.name}}</div>
                <div class="school"><img :src="item.homeTeam.logo" alt="">{{item.awayTeam.name}}</div>
            </div>
            <div class="match-score">
                <div :class="getResultText(item)=='W'|| ? 'result-win' : (getResultText(item)==='D' ? 'result-draw' : 'result-lost') " >{{item.homeScore}}</div>
                <div :class="getResultText(item)=='W' ? 'result-win' : (getResultText(item)==='D' ? 'result-draw' : 'result-lost') " >{{item.awayScore}}</div>
            </div>
            <div class="match-result">
                
                <p :class="getResultText(item)=='W' ? 'result-win' : (getResultText(item)==='D' ? 'result-draw' : 'result-lost') " >{{getResultText(item)}}</p>
            </div>
            <div class='match-detail'><p>详情</p></div>
      </li>
    </ul>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import schoolLogo from '@/assets/images/school_icon.png'

defineOptions({
    name: 'match-data-filter'
})

// 比赛数据
const matchData = ref([
    {
        id: 1,
        homeTeam: {
            name: '南外文华学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 0,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛-第一轮'
    },
    {
        id: 2,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛'
    },
    {
        id: 3,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '某某足球杯-第一轮'
    },
    {
        id: 4,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '某某足球杯-第一轮'
    }
])

// 获取比赛结果类名
const getResultClass = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'win'
    } else if (match.homeScore < match.awayScore) {
        return 'lose'
    } else {
        return 'draw'
    }
}

// 获取比赛结果文本
const getResultText = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'W'
    } else if (match.homeScore < match.awayScore) {
        return 'L'
    } else {
        return 'D'
    }
}
</script>
<style scoped lang="scss">
.match-data-filter {
  padding: 20px;
  padding-top: 0;
  // 响应式设计
    .match-list{
        overflow: hidden;
        color: #fff;
        font-size: 17px;
        font-weight: 500;
        .match-item{
            position: relative;
            float: left;
            display: flex;
            padding-left: 22px;
            height: 100px;
            box-sizing: border-box;
            width: 50%;
            align-items: center;
            &:nth-child(4n+1){
                background-color: rgba(8,139,255,0.1);
                border-radius: 3px 0 0 3px;
            }
            &:nth-child(4n+2){
                background-color: rgba(8,139,255,0.1);
                border-radius: 0 3px 3px 0;
            }
            &:nth-child(2n+1){
                ::after{
                    position: absolute;
                    content: '';
                    width: 1px;
                    height: 74px;
                    background-color: rgba(255,255,255,0.5);
                    right: 0px;
                    top: 13px;
                }
            }
            .match-info{
                width: 250px;
            }
            .school{
                width: 180px;
                overflow: hidden;
                margin-top: 5px;
                margin-bottom: 5px;
                img{
                    vertical-align: middle;
                    margin-right: 20px;
                    width: 24px;
                    height: 24px;
                }            
            }
            .match-score{
                width: 20px;
                div{
                    margin-bottom: 5px;
                    margin-top: 5px;
                }
                .win-color{
                    color: #fff;
                }
                .lose-color{
                    color: #7A7D86;
                }
                
            }
            .match-result{
                margin-left: 40px;
                margin-right: 60px;
                p{
                    width: 46px;
                    height: 46px;
                    line-height: 46px;
                    text-align: center;
                    border-radius: 50%;
                    border: 2px solid #BD1616;
                    color: #BD1616;
                }
                .result-win{
                    border: 2px solid #BD1616;
                    color: #BD1616;
                }
                .result-draw{
                    border-color: #C95C14;
                    color: #C95C14;
                }
                .result-lost{
                    border-color: #199A71;
                    color: #199A71;
                }
            }
            .match-detail{
                cursor: pointer;
                p{  min-width:80px;
                    text-align: center;
                    font-size: 15px;
                    padding: 2px 10px;
                    color: #13F0C6;
                    border-radius: 16px;
                    border: 1px solid #13F0C6;
                }
            }
        }  
    }
}

</style>
