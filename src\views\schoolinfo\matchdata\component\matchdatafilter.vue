<template>
  <div class="match-data-filter">
    <div class="match-list">
      <div
        v-for="(match, index) in matchData"
        :key="index"
        class="match-item"
      >
        <div class="match-info">
          <div class="match-teams">
            <div class="team home-team">
              <img :src="match.homeTeam.logo" :alt="match.homeTeam.name" class="team-logo">
              <span class="team-name">{{ match.homeTeam.name }}</span>
            </div>
            <div class="match-score">
              <span class="score">{{ match.homeScore }}</span>
              <span class="separator">:</span>
              <span class="score">{{ match.awayScore }}</span>
            </div>
            <div class="team away-team">
              <img :src="match.awayTeam.logo" :alt="match.awayTeam.name" class="team-logo">
              <span class="team-name">{{ match.awayTeam.name }}</span>
            </div>
          </div>
          <div class="match-details">
            <div class="match-date">{{ match.date }}</div>
            <div class="match-time">{{ match.time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import schoolLogo from '@/assets/images/school_icon.png'

defineOptions({
    name: 'match-data-filter'
})

// 比赛数据
const matchData = ref([
    {
        id: 1,
        homeTeam: {
            name: '南文学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: '南外文华学校',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 0,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛-第一轮'
    },
    {
        id: 2,
        homeTeam: {
            name: '南文学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 0,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛-第一轮'
    }
])
</script>
<style scoped lang="scss">
.match-data-filter {
  padding: 20px;

  .match-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .match-item {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 16px 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);

      .match-info {
        .match-teams {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .team {
            display: flex;
            align-items: center;
            flex: 1;

            &.home-team {
              justify-content: flex-start;
            }

            &.away-team {
              justify-content: flex-end;
              flex-direction: row-reverse;

              .team-name {
                margin-right: 8px;
                margin-left: 0;
              }
            }

            .team-logo {
              width: 24px;
              height: 24px;
              object-fit: contain;
            }

            .team-name {
              font-size: 16px;
              font-weight: 600;
              color: #ffffff;
              margin-left: 8px;
            }
          }

          .match-score {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 20px;

            .score {
              font-size: 24px;
              font-weight: 700;
              color: #2DD8B7;
              min-width: 20px;
              text-align: center;
            }

            .separator {
              font-size: 20px;
              font-weight: 600;
              color: rgba(255, 255, 255, 0.6);
            }
          }
        }

        .match-details {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;

          .match-date,
          .match-time {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 15px;

    .match-list {
      gap: 12px;

      .match-item {
        padding: 12px 16px;

        .match-info {
          .match-teams {
            .team {
              .team-logo {
                width: 20px;
                height: 20px;
              }

              .team-name {
                font-size: 14px;
                margin-left: 6px;
              }
            }

            .match-score {
              margin: 0 10px;

              .score {
                font-size: 20px;
              }

              .separator {
                font-size: 16px;
              }
            }
          }

          .match-details {
            .match-date,
            .match-time {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
