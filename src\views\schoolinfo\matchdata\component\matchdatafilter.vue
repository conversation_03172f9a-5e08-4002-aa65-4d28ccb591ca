<template>
  <div class="match-data-filter">
    <ul class="match-list">
      <li class="match-item" v-for="item in matchData" :key="item.id">
            <div class="match-info">
                <p>{{item.matchType}}</p>
                <p>{{item.date}} {{item.time}}</p>
            </div>
            <div class="match-shcool">
                <div class="school"><img :src="item.homeTeam.logo" alt="">{{item.homeTeam.name}}</div>
                <div class="school"><img :src="item.awayTeam.logo" alt="">{{item.awayTeam.name}}</div>
            </div>
            <div class="match-score">
                <div :class="getHomeScoreClass(item)">{{item.homeScore}}</div>
                <div :class="getAwayScoreClass(item)">{{item.awayScore}}</div>
            </div>
            <div class="match-result">
                <p :class="getResultClass(item)">{{getResultText(item)}}</p>
            </div>
            <div class='match-detail'><p>详情</p></div>
      </li>
    </ul>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { watchRoute } from '@/utils/watchRoute';
import schoolLogo from '@/assets/images/school_icon.png'

let originalData = ref(); // 保存原始完整数据
// 比赛数据
const matchData = ref([
    {
        id: 1,
        homeTeam: {
            name: '南外文华学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 0,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛-第一轮'
    },
    {
        id: 2,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛'
    },
    {
        id: 3,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '某某足球杯-第一轮'
    },
    {
        id: 4,
        homeTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '某某足球杯-第一轮'
    }
])

onMounted(() => {
  watchRoute((newQuery, oldQuery) => {
    console.log('123')
      // 监听key参数的变化
      if (newQuery.key !== oldQuery?.key) {
        console.log('key参数变化:', oldQuery?.key, '->', newQuery.key);
        handleKeyChange(newQuery.key);
      }
    }, {
      immediate: true // 如果需要立即执行，可以传入这个选项
  });
});

function handleKeyChange(key) {
   if(key=='全部') {

   }else{
        
   }
}

// 获取比赛结果文本
const getResultText = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'W'
    } else if (match.homeScore < match.awayScore) {
        return 'L'
    } else {
        return 'D'
    }
}

// 获取主队比分样式
const getHomeScoreClass = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'win-color'  // 主队胜利
    } else if (match.homeScore === match.awayScore) {
        return 'win-color'  // 平局
    } else {
        return 'lose-color' // 主队失败
    }
}

// 获取客队比分样式
const getAwayScoreClass = (match) => {
    if (match.awayScore > match.homeScore) {
        return 'win-color'  // 客队胜利
    } else if (match.awayScore === match.homeScore) {
        return 'win-color'  // 平局
    } else {
        return 'lose-color' // 客队失败
    }
}

// 获取胜负平徽章样式
const getResultClass = (match) => {
    const result = getResultText(match)
    if (result === 'W') {
        return 'result-win'   // 胜利 - 红色
    } else if (result === 'D') {
        return 'result-draw'  // 平局 - 橙色
    } else {
        return 'result-lost'  // 失败 - 绿色
    }
}
</script>
<style scoped lang="scss">
.match-data-filter {
  padding: 20px;
  padding-top: 0;
  // 响应式设计
    .match-list{
        overflow: hidden;
        color: #fff;
        font-size: 17px;
        font-weight: 500;
        .match-item{
            position: relative;
            float: left;
            display: flex;
            padding-left: 22px;
            height: 100px;
            box-sizing: border-box;
            width: 50%;
            align-items: center;
            &:nth-child(4n+1){
                background-color: rgba(8,139,255,0.1);
                border-radius: 3px 0 0 3px;
            }
            &:nth-child(4n+2){
                background-color: rgba(8,139,255,0.1);
                border-radius: 0 3px 3px 0;
            }
            &:nth-child(2n+1){
                ::after{
                    position: absolute;
                    content: '';
                    width: 1px;
                    height: 74px;
                    background-color: rgba(255,255,255,0.5);
                    right: 0px;
                    top: 13px;
                }
            }
            .match-info{
                width: 250px;
            }
            .school{
                width: 180px;
                overflow: hidden;
                margin-top: 5px;
                margin-bottom: 5px;
                img{
                    vertical-align: middle;
                    margin-right: 20px;
                    width: 24px;
                    height: 24px;
                }            
            }
            .match-score{
                width: 20px;
                div{
                    margin-bottom: 5px;
                    margin-top: 5px;
                }
                .win-color{
                    color: #fff;
                }
                .lose-color{
                    color: #7A7D86;
                }
                
            }
            .match-result{
                margin-left: 40px;
                margin-right: 60px;
                p{
                    width: 46px;
                    height: 46px;
                    line-height: 46px;
                    text-align: center;
                    border-radius: 50%;
                    border: 2px solid #BD1616;
                    color: #BD1616;
                }
                .result-win{
                    border: 2px solid #BD1616;
                    color: #BD1616;
                }
                .result-draw{
                    border-color: #C95C14;
                    color: #C95C14;
                }
                .result-lost{
                    border-color: #199A71;
                    color: #199A71;
                }
            }
            .match-detail{
                cursor: pointer;
                p{  min-width:80px;
                    text-align: center;
                    font-size: 15px;
                    padding: 2px 10px;
                    color: #13F0C6;
                    border-radius: 16px;
                    border: 1px solid #13F0C6;
                }
            }
        }  
    }
}

</style>
