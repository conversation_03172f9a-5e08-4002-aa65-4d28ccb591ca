<template>
  <div class="match-data-filter">
    <ul class="match-list">
      <li
        v-for="(matchPair, index) in matchPairs"
        :key="index"
        class="match-row"
      >
        <div
          v-for="(match, matchIndex) in matchPair"
          :key="matchIndex"
          class="match-item"
        >
          <div class="match-info">
            <div class="match-header">
              <div class="match-type">{{ match.matchType }}</div>
              <div class="match-datetime">
                <span class="match-date">{{ match.date }}</span>
                <span class="match-time">{{ match.time }}</span>
              </div>
            </div>
            <div class="match-teams">
              <div class="team home-team">
                <img :src="match.homeTeam.logo" :alt="match.homeTeam.name" class="team-logo">
                <span class="team-name">{{ match.homeTeam.name }}</span>
              </div>
              <div class="match-score">
                <span class="score">{{ match.homeScore }}</span>
              </div>
              <div class="team away-team">
                <img :src="match.awayTeam.logo" :alt="match.awayTeam.name" class="team-logo">
                <span class="team-name">{{ match.awayTeam.name }}</span>
              </div>
              <div class="match-score">
                <span class="score">{{ match.awayScore }}</span>
              </div>
            </div>
            <div class="match-result">
              <div :class="['result-badge', getResultClass(match)]">
                {{ getResultText(match) }}
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import schoolLogo from '@/assets/images/school_icon.png'

defineOptions({
    name: 'match-data-filter'
})

// 比赛数据
const matchData = ref([
    {
        id: 1,
        homeTeam: {
            name: '南文学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: '南外文华学校',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 0,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛-第一轮'
    },
    {
        id: 2,
        homeTeam: {
            name: '南文学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛'
    },
    {
        id: 3,
        homeTeam: {
            name: '南外文华学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LYON EDG',
            logo: schoolLogo
        },
        homeScore: 0,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛'
    },
    {
        id: 4,
        homeTeam: {
            name: '南文学校',
            logo: schoolLogo
        },
        awayTeam: {
            name: 'LOOP RNG',
            logo: schoolLogo
        },
        homeScore: 2,
        awayScore: 2,
        date: '2025/04/20',
        time: '16:30',
        matchType: '友谊赛'
    }
])

// 将比赛数据按每行两个分组
const matchPairs = computed(() => {
    const pairs = []
    for (let i = 0; i < matchData.value.length; i += 2) {
        const pair = matchData.value.slice(i, i + 2)
        pairs.push(pair)
    }
    return pairs
})

// 获取比赛结果类名
const getResultClass = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'win'
    } else if (match.homeScore < match.awayScore) {
        return 'lose'
    } else {
        return 'draw'
    }
}

// 获取比赛结果文本
const getResultText = (match) => {
    if (match.homeScore > match.awayScore) {
        return 'W'
    } else if (match.homeScore < match.awayScore) {
        return 'L'
    } else {
        return 'D'
    }
}
</script>
<style scoped lang="scss">
.match-data-filter {
  padding: 20px;

  .match-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .match-row {
      display: flex;
      gap: 16px;

      .match-item {
        flex: 1;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 16px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);

        .match-info {
          .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .match-type {
              font-size: 14px;
              font-weight: 600;
              color: #ffffff;
            }

            .match-datetime {
              display: flex;
              align-items: center;
              gap: 8px;

              .match-date,
              .match-time {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }

          .match-teams {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .team {
              display: flex;
              align-items: center;
              flex: 1;

              .team-logo {
                width: 20px;
                height: 20px;
                object-fit: contain;
                margin-right: 6px;
              }

              .team-name {
                font-size: 14px;
                font-weight: 600;
                color: #ffffff;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .match-score {
              .score {
                font-size: 20px;
                font-weight: 700;
                color: #2DD8B7;
                min-width: 20px;
                text-align: center;
                margin: 0 4px;
              }
            }
          }

          .match-result {
            display: flex;
            justify-content: center;

            .result-badge {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 700;
              color: #ffffff;

              &.win {
                background-color: #22c55e;
              }

              &.lose {
                background-color: #ef4444;
              }

              &.draw {
                background-color: #6b7280;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 15px;

    .match-list {
      gap: 12px;

      .match-row {
        flex-direction: column;
        gap: 12px;

        .match-item {
          padding: 12px;

          .match-info {
            .match-header {
              .match-type {
                font-size: 12px;
              }

              .match-datetime {
                .match-date,
                .match-time {
                  font-size: 10px;
                }
              }
            }

            .match-teams {
              .team {
                .team-logo {
                  width: 16px;
                  height: 16px;
                  margin-right: 4px;
                }

                .team-name {
                  font-size: 12px;
                }
              }

              .match-score {
                .score {
                  font-size: 16px;
                  margin: 0 2px;
                }
              }
            }

            .match-result {
              .result-badge {
                width: 20px;
                height: 20px;
                font-size: 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
