<template>
	<div class="transfer-records">
		<el-table :data="transferRecords" style="width: 100%" class="custom-table"  stripe
            :cell-style="{color:'#fff',background: 'transparent'}"
            :header-row-style="{color:'#fff',background: 'rgba(8,139,255,0.1)'}"
            :row-class-name="tableRowClassName"
            >
                <el-table-column prop="year" label="时间" />
                <el-table-column prop="name" label="姓名" width="180">
                    <template #default="scope">
                        <div class="name-cell">
                            <img :src="scope.row.avatar" alt="" class="avatar">
                            <span>{{ scope.row.name }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="position" label="位置" />
                <el-table-column prop="transferTo" label="单位" />
                <el-table-column prop="level" label="状态" />
                <el-table-column prop="aiArchives" label="档案">
                    <template #default="scope">
                        <div v-if="scope.row.aiArchives" class="aiArchives">AI档案</div>
                        <span v-else></span>
                    </template>
                </el-table-column>
        </el-table>
	</div>
</template>
<script setup>
import { ref } from 'vue'
import studentImg from '@/assets/images/coach-01.png'
// 人才转会记录数据
const transferRecords = ref([
    {
        id: 1,
        year: '2024年',
        name: '刘某某',
        avatar:studentImg,
        position: '前锋',
        transferTo: 'XX足球俱乐部训练队',
        level: 'U17梯队主力',
        aiArchives: true
    },
    {
        id: 2,
        year: '2024年',
        avatar:studentImg,
        name: '张某某',
        position: '中场',
        transferTo: 'XX足球俱乐部青训营',
        level: 'U16梯队核心',
        aiArchives: false
    },
    {
        id: 3,
        year: '2023年',
        avatar:studentImg,
        name: '王某某',
        position: '后卫',
        transferTo: 'XX足球俱乐部预备队',
        level: 'U18梯队队长',
        aiArchives: true
    },
    {
        id: 4,
        year: '2023年',
        name: '李某某',
        avatar:studentImg,
        position: '守门员',
        transferTo: 'XX足球俱乐部一线队',
        level: 'U19梯队主力',
        aiArchives: true
    }
])
//table 结合内置方法改变th、td样式，隔行变色设置
function tableRowClassName({ row, rowIndex }) {
	if (rowIndex % 2 === 1) {
		return "warning-row";
	} else {
		return "success-row";
	}
}
</script>
<style src="@/styles/table.scss" lang="scss" scoped></style>
<style scoped lang="scss">
    .name-cell{
  display: flex;
  align-items: center;
  .avatar{
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }
  .isTop{
    height: 16px;
    line-height: 16px;
    border: 1px solid #FF8B00;
    color: #FF8B00;
    padding: 0px 4px;
    border-radius: 9px;
    font-size: 9px;
    margin-left: 10px;
  }
}
</style>
