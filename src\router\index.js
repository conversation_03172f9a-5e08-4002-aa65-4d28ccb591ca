import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const privateRoutes = [];
const publicRoutes = [{
        path: '/login',
        name: 'login',
        component: () =>
            import ('@/views/login/index.vue'),
    },
    {
        path: '/401',
        name: '401',
        component: () =>
            import ('@/views/error-page/401.vue'),
    },
    {
        path: '/404',
        name: '404',
        component: () =>
            import ('@/views/error-page/404.vue'),
    },
    {
        path: '/',
        name: 'home',
        component: () =>
            import ('@/views/home/<USER>'),
        children: [{
            path: '/elite',
            name: 'elite',
            component: () =>
                import ('@/views/elite/index.vue')
        }, {
            path: '/event',
            name: 'event',
            component: () =>
                import ('@/views/event/index.vue')
        }, {
            path: '/school',
            name: 'school',
            component: () =>
                import ('@/views/school/index.vue')
        }, {
            path: '/echelon',
            name: 'echelon',
            component: () =>
                import ('@/views/echelon/index.vue')
        }, {
            path: '/schoolinfo',
            name: 'schoolinfo',
            component: () =>
                import ('@/views/schoolinfo/index.vue')
        }]
    }
];


const router = createRouter({
    history: createWebHistory(
        import.meta.env.BASE_URL),
    routes: [
        ...publicRoutes,
        ...privateRoutes
    ],
})

export default router