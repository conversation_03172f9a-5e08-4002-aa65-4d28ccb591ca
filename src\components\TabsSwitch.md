# TabsSwitch 切换插件

一个功能强大的Vue 3标签切换组件，支持动态组件加载、多种动画效果和流畅的下划线过渡。

## 功能特性

- ✨ **动态组件加载**: 根据组件名动态加载不同组件
- 🎯 **下划线过渡**: 流畅的下划线跟随动画
- 🎨 **多种动画**: 支持滑动、淡入淡出、缩放三种切换动画
- 📱 **响应式设计**: 自适应不同屏幕尺寸
- 🔧 **高度可定制**: 支持传递props、事件监听等
- 🚀 **性能优化**: 使用Vue 3 Composition API，性能卓越

## 基础用法

```vue
<template>
  <TabsSwitch 
    :tabs="tabs"
    @change="onTabChange"
  />
</template>

<script setup>
import TabsSwitch from '@/components/TabsSwitch.vue'
import ComponentA from './ComponentA.vue'
import ComponentB from './ComponentB.vue'

const tabs = ref([
  { name: '标签1', component: ComponentA },
  { name: '标签2', component: ComponentB }
])

const onTabChange = (data) => {
  console.log('切换到:', data.tab.name)
}
</script>
```

## API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| tabs | Array | [] | 标签配置数组，每项包含 name 和 component |
| defaultActive | Number | 0 | 默认激活的标签索引 |
| componentProps | Object | {} | 传递给组件的props |
| animationType | String | 'slide' | 动画类型：slide/fade/scale |

### tabs 数组格式

```javascript
[
  {
    name: '标签名称',        // 显示的标签文字
    component: ComponentName // Vue组件
  }
]
```

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | { index, tab, oldIndex } | 标签切换时触发 |
| tab-click | { index, tab } | 标签点击时触发 |

### 暴露的方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| switchTab | index | 程序化切换到指定标签 |

## 动画类型

### 1. 滑动动画 (slide)
```vue
<TabsSwitch 
  :tabs="tabs"
  animation-type="slide"
/>
```

### 2. 淡入淡出 (fade)
```vue
<TabsSwitch 
  :tabs="tabs"
  animation-type="fade"
/>
```

### 3. 缩放动画 (scale)
```vue
<TabsSwitch 
  :tabs="tabs"
  animation-type="scale"
/>
```

## 高级用法

### 传递Props给组件

```vue
<template>
  <TabsSwitch 
    :tabs="tabs"
    :component-props="componentProps"
  />
</template>

<script setup>
const componentProps = ref({
  customData: '传递的数据',
  theme: 'dark'
})
</script>
```

### 程序化控制

```vue
<template>
  <TabsSwitch 
    ref="tabsRef"
    :tabs="tabs"
  />
  <button @click="switchToSecond">切换到第二个标签</button>
</template>

<script setup>
const tabsRef = ref()

const switchToSecond = () => {
  tabsRef.value.switchTab(1)
}
</script>
```

### 监听切换事件

```vue
<template>
  <TabsSwitch 
    :tabs="tabs"
    @change="onTabChange"
    @tab-click="onTabClick"
  />
</template>

<script setup>
const onTabChange = ({ index, tab, oldIndex }) => {
  console.log(`从标签${oldIndex}切换到标签${index}`)
  console.log('当前标签:', tab.name)
}

const onTabClick = ({ index, tab }) => {
  console.log('点击了标签:', tab.name)
}
</script>
```

## 样式定制

组件使用CSS变量，可以轻松定制样式：

```scss
.tabs-switch {
  --tab-bg: rgba(255, 255, 255, 0.1);
  --tab-active-color: #fff;
  --underline-color: #00d4ff;
  --transition-duration: 0.4s;
}
```

## 注意事项

1. 确保传入的组件已正确导入
2. 组件名可以是字符串或组件对象
3. 下划线动画依赖于DOM元素的实际尺寸
4. 建议在组件挂载后再进行程序化切换

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 55
- Safari >= 12
- Edge >= 79
