<template>
  <div class="player-list">
    <div class="list-header">
      <h3>{{ positionTitle }} ({{ players.length }}人)</h3>
    </div>
    
    <div class="players-grid">
      <div 
        v-for="player in players" 
        :key="player.id"
        class="player-card"
      >
        <div class="player-number">{{ player.number }}</div>
        <div class="player-info">
          <div class="player-name">{{ player.name }}</div>
          <div class="player-position">{{ player.position }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="players.length === 0" class="empty-state">
      <div class="empty-icon">⚽</div>
      <div class="empty-text">暂无球员数据</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 从TabsFilter传递过来的筛选数据
  filterData: {
    type: Object,
    default: () => ({})
  }
})

// 计算属性
const players = computed(() => {
  return props.filterData?.players || []
})

const positionTitle = computed(() => {
  const positionMap = {
    'all': '全部球员',
    'forward': '前锋球员',
    'midfielder': '中场球员',
    'defender': '后卫球员',
    'goalkeeper': '守门员'
  }
  return positionMap[props.filterData?.position] || '球员列表'
})
</script>

<style scoped lang="scss">
.player-list {
  .list-header {
    margin-bottom: 20px;
    
    h3 {
      color: #fff;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }
  }
  
  .players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    
    .player-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }
      
      .player-number {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #2DD8B7, #1BC5A8);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-weight: 700;
        font-size: 16px;
        margin-right: 12px;
        flex-shrink: 0;
      }
      
      .player-info {
        flex: 1;
        
        .player-name {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .player-position {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    .empty-text {
      color: rgba(255, 255, 255, 0.6);
      font-size: 16px;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .players-grid {
      grid-template-columns: 1fr;
      gap: 12px;
      
      .player-card {
        padding: 12px;
      }
    }
  }
}
</style>
